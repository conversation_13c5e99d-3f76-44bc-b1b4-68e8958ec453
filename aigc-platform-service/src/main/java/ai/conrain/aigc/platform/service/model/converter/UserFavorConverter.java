package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.UserFavorDO;
import ai.conrain.aigc.platform.service.enums.FavorTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.FavorCreationModel;
import ai.conrain.aigc.platform.service.model.biz.FavorImageModel;
import ai.conrain.aigc.platform.service.model.query.UserFavorQuery;
import ai.conrain.aigc.platform.dal.example.UserFavorExample;
import ai.conrain.aigc.platform.service.model.request.UserFavorRequest;
import ai.conrain.aigc.platform.service.model.vo.LoraOption;
import ai.conrain.aigc.platform.service.model.vo.UserFavorVO;

import ai.conrain.aigc.platform.service.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IMAGE_INDEXES;

/**
 * UserFavorConverter
 *
 * @version UserFavorService.java v 0.1 2025-03-08 11:26:59
 */
public class UserFavorConverter {

    /**
     * DO -> VO
     */
    public static UserFavorVO do2VO(UserFavorDO from) {
        UserFavorVO to = new UserFavorVO();
        to.setId(from.getId());
        to.setType(FavorTypeEnum.getByCode(from.getType()));
        to.setItemId(from.getItemId());
        to.setModelId(from.getModelId());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(CommonUtil.parseObject(from.getExtInfo()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static UserFavorDO vo2DO(UserFavorVO from) {
        UserFavorDO to = new UserFavorDO();
        to.setId(from.getId());
        to.setType(from.getType().getCode());
        to.setItemId(from.getItemId());
        to.setModelId(from.getModelId());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toString() : null);

        return to;
    }

    /**
     * DO -> Query
     */
    public static UserFavorQuery do2Query(UserFavorDO from) {
        UserFavorQuery to = new UserFavorQuery();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setItemId(from.getItemId());
        to.setModelId(from.getModelId());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    public static UserFavorQuery vo2Query(UserFavorVO from) {
        UserFavorQuery to = new UserFavorQuery();
        to.setId(from.getId());
        to.setType(from.getType().getCode());
        to.setItemId(from.getItemId());
        to.setModelId(from.getModelId());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());

        return to;
    }

    /**
     * Query -> DO
     */
    public static UserFavorDO query2DO(UserFavorQuery from) {
        UserFavorDO to = new UserFavorDO();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setItemId(from.getItemId());
        to.setModelId(from.getModelId());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }


    /**
     * Query -> Example
     */
    public static UserFavorExample query2Example(UserFavorQuery from) {
        UserFavorExample to = new UserFavorExample();
        UserFavorExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getItemId())) {
            c.andItemIdEqualTo(from.getItemId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getModelId())) {
            c.andModelIdEqualTo(from.getModelId());
        }
        if (!ObjectUtils.isEmpty(from.getMemo())) {
            c.andMemoEqualTo(from.getMemo());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }

        if (!ObjectUtils.isEmpty(from.getDateFrom())) {
            c.andModifyTimeGreaterThanOrEqualTo(DateUtils.parseShort(from.getDateFrom()));
        }

        if (!ObjectUtils.isEmpty(from.getDateTo())) {
            c.andModifyTimeLessThanOrEqualTo(DateUtils.parseShortLastTime(from.getDateTo()));
        }

        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<UserFavorVO> doList2VOList(List<UserFavorDO> list) {
        return CommonUtil.listConverter(list, UserFavorConverter::do2VO);
    }

    public static UserFavorVO request2VO(UserFavorRequest request) {
        UserFavorVO to = new UserFavorVO();
        to.setId(request.getId());
        to.setType(FavorTypeEnum.getByCode(request.getType()));
        to.setItemId(request.getItemId());
        to.setModelId(request.getModelId());
        to.setExtInfo(new JSONObject());
        if (StringUtils.equals(request.getType(), FavorTypeEnum.MATERIAL.getCode())) {
            to.setModelId(request.getItemId());
        }
        if (!CollectionUtils.isEmpty(request.getImages())) {
            to.addExtInfo(KEY_IMAGE_INDEXES, request.getImages());
        }
        return to;
    }

    /**
     * do -> model
     */
    public static FavorCreationModel do2Model(UserFavorDO from) {
        FavorCreationModel to = new FavorCreationModel();
        to.setModelId(from.getModelId());
        to.setModelName(from.getModelName());
        to.setType(from.getType());
        to.setShowImage(from.getShowImage());
        to.setImageCount(from.getImageCount());

        return to;
    }

    /**
     * do -> loraOpt
     */
    public static LoraOption do2LoraOpt(UserFavorDO from) {
        LoraOption to = new LoraOption();
        to.setId(from.getModelId());
        to.setName(from.getModelName());

        return to;
    }


}