package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

// 一级分类枚举
@Getter
public enum ClothStyleFirstCategory {
    外套("Outerwear", "外套"),
    冬季外套("Winter_Outerwear", "冬季外套"),
    上衣("Tops", "上衣"),
    半裙("Skirts", "半裙"),
    裤子("Pants", "裤子"),
    连衣裙("Dress", "连衣裙"),
    连体衣("Jumpsuit", "连体衣"),
    套装("Suits", "套装");

    private final String code;
    private final String desc;

    ClothStyleFirstCategory(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ClothStyleFirstCategory getByCode(String code) {
        for (ClothStyleFirstCategory category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }
}
