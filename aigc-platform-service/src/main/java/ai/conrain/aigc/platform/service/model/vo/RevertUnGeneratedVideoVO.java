package ai.conrain.aigc.platform.service.model.vo;

import java.util.List;
import java.util.Map;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RevertUnGeneratedVideoVO {
    /** 要退点的对象 */
    @NotBlank
    private List<Map<String,String>> revertUnGeneratedVideoList;
    /** 批次id */
    @NotNull
    private Integer batchId;

}
