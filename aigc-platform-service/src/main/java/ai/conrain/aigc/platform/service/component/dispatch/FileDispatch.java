/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.dispatch;

import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import java.io.IOException;
import java.io.InputStream;

/**
 * 文件派发服务
 *
 * <AUTHOR>
 * @version : FileDispatchService.java, v 0.1 2024/9/27 10:21 renxiao.wu Exp $
 */
public interface FileDispatch {

    /**
     * 上传文件
     *
     * @param path          文件名+文件路径
     * @param inputStream   输入流
     * @param fileServerUrl 文件服务地址
     * @param serverId      服务id
     */
    void uploadFile(String path, InputStream inputStream, String fileServerUrl, Integer serverId) throws IOException;

    /**
     * 上传文件
     *
     * @param path         文件名+文件路径
     * @param inputStream  输入流
     * @param masterUserId 主账号id
     */
    void uploadFile(String path, InputStream inputStream, Integer masterUserId) throws IOException;

    /**
     * 上传文件
     *
     * @param path         文件名+文件路径
     * @param inputStream  输入流
     * @param masterUserId 主账号id
     * @param orderly      是否需要排序
     */
    void uploadFile(String path, InputStream inputStream, Integer masterUserId, boolean orderly) throws IOException;

    /**
     * 上传文件
     *
     * @param path         文件名+文件路径
     * @param inputStream  输入流
     * @param masterUserId 主账号id
     * @param orderly      是否需要排序
     */
    void uploadFile(String path, InputStream inputStream, Integer masterUserId, boolean orderly, ServerVO server)
        throws IOException;

    /**
     * 上传文件到 ComfyUI, 并从OSS同步到 ComfyUI
     *
     * @param path         文件名+文件路径
     * @param inputStream  输入流
     * @param ossUrl       oss地址
     * @param masterUserId 主账号id
     * @param orderly      是否需要排序
     * @param fileServer   指定的server
     */
    void uploadFile(String path, InputStream inputStream, String ossUrl, Integer masterUserId, boolean orderly,
                    ServerVO fileServer, String md5) throws IOException;

    /**
     * 文件夹拷贝服务
     *
     * @param path          文件的全路径
     * @param newPath       新文件路径
     * @param fileServerUrl 目标服务地址
     */
    void folderCopy(String path, String newPath, String fileServerUrl);

    /**
     * 通知文件同步
     *
     * @param fileServerUrl 当前文件所在服务地址
     * @param path          文件名
     * @param orderly       是否需要排序
     */
    void notifyFileSync(String fileServerUrl, String path, boolean orderly);

    /**
     * 通知文件同步
     *
     * @param fileServerUrl 当前文件所在服务地址
     * @param path          文件名
     * @param ossUrl        文件地址
     */
    void notifyFileSync(String fileServerUrl, String path, String ossUrl);

    /**
     * 通知文件同步
     *
     * @param fileServerUrl 当前文件所在服务地址
     * @param path          文件名
     * @param ossUrl        文件地址
     */
    void notifyFileSync(String fileServerUrl, String path, String ossUrl, String md5);

    /**
     * 通知文件同步
     *
     * @param fileServerUrl 当前文件所在服务地址
     * @param path          文件名
     * @param ossUrl        文件地址
     * @param md5           文件md5
     * @param loraId        loraID
     */
    void notifyFileSync(String fileServerUrl, String path, String ossUrl, String md5, Integer loraId);

    /**
     * 通知文件夹同步
     *
     * @param fileServerUrl 当前文件所在服务地址
     * @param path          文件夹全路径
     * @param orderly       是否需要排序
     */
    void notifyFolderSync(String fileServerUrl, String path, boolean orderly);

    /**
     * 通知文件夹同步
     *
     * @param fileServerUrl 当前文件所在服务地址
     * @param path          文件夹全路径
     * @param md5           md5
     */
    void notifyFolderSync(String fileServerUrl, String path, String md5);

    /**
     * 通知文件夹同步
     *
     * @param fileServerUrl 当前文件所在服务地址
     * @param path          文件夹全路径
     * @param md5           md5
     * @param samePipeline  是否在同一个pipeline下
     */
    void notifyFolderSync(String fileServerUrl, String path, String md5, boolean samePipeline);

    /**
     * 重命名文件
     *
     * @param path    文件名+文件路径
     * @param newPath 新路径
     */
    void renameFile(String path, String newPath);

    /**
     * 更新远程文本文件内容
     *
     * @param filePath  文件路径
     * @param content   内容
     * @param serverUrl 服务地址
     */
    void updateTextFileContent(String filePath, String content, String serverUrl);

    /**
     * 远程删除图片或txt文件
     *
     * @param dir            文件路径
     * @param fileNamePrefix 文件名前缀
     * @param serverUrl      文件服务地址
     */
    void removeImageOrTxtFile(String dir, String fileNamePrefix, String serverUrl);
}
