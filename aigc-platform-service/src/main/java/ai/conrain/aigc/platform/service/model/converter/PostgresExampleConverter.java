package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.PostgresExampleDO;
import ai.conrain.aigc.platform.service.model.query.PostgresExampleQuery;
import ai.conrain.aigc.platform.dal.example.PostgresExampleExample;
import ai.conrain.aigc.platform.service.model.vo.PostgresExampleVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * PostgresExampleConverter
 *
 * @version PostgresExampleService.java v 0.1 2025-07-22 04:16:39
 */
public class PostgresExampleConverter {

    /**
     * DO -> VO
     */
    public static PostgresExampleVO do2VO(PostgresExampleDO from) {
        PostgresExampleVO to = new PostgresExampleVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setClothVector(from.getClothVector());

        return to;
    }

    /**
     * VO -> DO
     */
    public static PostgresExampleDO vo2DO(PostgresExampleVO from) {
        PostgresExampleDO to = new PostgresExampleDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setClothVector(from.getClothVector());

        return to;
    }

    /**
     * Query -> Example
     */
    public static PostgresExampleExample query2Example(PostgresExampleQuery from) {
        PostgresExampleExample to = new PostgresExampleExample();
        PostgresExampleExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getClothVector())) {
            c.andClothVectorEqualTo(from.getClothVector());
        }
        //逻辑删除过滤
        for (PostgresExampleExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<PostgresExampleVO> doList2VOList(List<PostgresExampleDO> list) {
        return CommonUtil.listConverter(list, PostgresExampleConverter::do2VO);
    }
}