package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.TestResultQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.ExtInfoAnalysisRatioVO;
import ai.conrain.aigc.platform.service.model.vo.ResultAnalysisRatioVO;
import ai.conrain.aigc.platform.service.model.vo.TestItemGroupVO;
import ai.conrain.aigc.platform.service.model.vo.ExtInfoReq;
import ai.conrain.aigc.platform.service.model.vo.TestResultVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * AB测试结果 Service定义
 *
 * <AUTHOR>
 * @version TestResultService.java v 0.1 2024-12-19 01:24:06
 */
public interface TestResultService {

    /**
     * 查询AB测试结果对象
     *
     * @param id 主键
     * @return 返回结果
     */
    TestResultVO selectById(Integer id);

    /**
     * 删除AB测试结果对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加AB测试结果对象
     *
     * @param testResult 对象参数
     * @return 返回结果
     */
    TestResultVO insert(TestResultVO testResult);

    /**
     * 修改AB测试结果对象
     *
     * @param testResult 对象参数
     */
    void updateByIdSelective(TestResultVO testResult);

    /**
     * 带条件批量查询AB测试结果列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<TestResultVO> queryTestResultList(TestResultQuery query);

    /**
     * 带条件查询AB测试结果数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryTestResultCount(TestResultQuery query);

    /**
     * 带条件分页查询AB测试结果
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<TestResultVO> queryTestResultByPage(TestResultQuery query);

    /**
     * 基于创作任务初始化
     *
     * @param tasks 创作任务列表
     * @param group ab测试项目分组
     */
    void initByTasks(List<CreativeTaskVO> tasks, TestItemGroupVO group);


    /**
     * 汇总分析打分
     *
     * @param id   实验项目id(或其他 Id)
     * @param type 分析结果类型
     * @return 分析结果
     */
    ResultAnalysisRatioVO resultAnalysisRatio(Integer id, String type);

    /**
     * 更新测试结果扩展信息
     * @param req 请求
     * @return 是否更新成功
     */
    Boolean updateExtInfo(ExtInfoReq req);
}