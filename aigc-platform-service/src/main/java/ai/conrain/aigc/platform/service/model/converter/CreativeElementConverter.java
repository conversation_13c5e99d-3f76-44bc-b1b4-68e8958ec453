package ai.conrain.aigc.platform.service.model.converter;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.CreativeElementDO;
import ai.conrain.aigc.platform.dal.example.CreativeElementExample;
import ai.conrain.aigc.platform.dal.example.CreativeElementExample.Criteria;
import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption.LensLanguage;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption.ShootingTheme;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.ElementStatusEnum;
import ai.conrain.aigc.platform.service.enums.LabelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelVersionEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.analysis.CompositionEnum;
import ai.conrain.aigc.platform.service.enums.analysis.ViewpointEnum;
import ai.conrain.aigc.platform.service.model.biz.StyleScene;
import ai.conrain.aigc.platform.service.model.biz.StyleScene.SceneDetail;
import ai.conrain.aigc.platform.service.model.query.CreativeElementQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.ElementUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_AGE_DESC;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ANALYSIS_JSON;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_CATEGORY;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_CATEGORY_OTHER;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_EXPRESSION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_RESTORE_VISIBILITY;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_HAIRSTYLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_LORA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LABEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LENS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_NOSHOW_FACE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_POSTURE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCENE_CLOTH_TYPE_SCOPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE_MODEL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE_OUTFIT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SWAP_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.util.ElementUtils.BACKGROUND_SCENE_IDS;
import static ai.conrain.aigc.platform.service.util.ElementUtils.CHILD_MODEL_TYPES;

/**
 * CreativeElementConverter
 *
 * @version CreativeElementService.java v 0.1 2024-05-09 06:10:02
 */
@Slf4j
public class CreativeElementConverter {

    /**
     * DO -> VO
     */
    public static CreativeElementVO do2VO(CreativeElementDO from) {
        CreativeElementVO to = new CreativeElementVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setConfigKey(from.getConfigKey());
        to.setLevel(from.getLevel());
        to.setParentId(from.getParentId());
        to.setShowImage(from.getShowImage());
        to.setStatus(ElementStatusEnum.getByCode(from.getStatus()));
        to.setOrder(from.getOrder());
        to.setMemo(from.getMemo());
        if (StringUtils.isNotBlank(from.getType())) {
            to.setType(Arrays.asList(from.getType().split(",")));
        }
        to.setBelong(ModelTypeEnum.getByCode(from.getBelong()));
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        to.setTags(ComfyUIUtils.parseJsonStr(from.getTags()));
        to.setExtTags(ComfyUIUtils.parseJsonStr(from.getExtTags()));
        to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));

        ModelVersionEnum version = StringUtils.equalsIgnoreCase("LORA", to.getExtInfo(KEY_SWAP_TYPE, String.class))
            ? ModelVersionEnum.FLUX : ModelVersionEnum.SDXL;
        to.setVersion(version.getCode());
        to.setStyleScene(ElementUtils.isStyleScene(to));

        to.setLoraModelId(from.getLoraModelId());
        to.setIsNew(from.getIsNew());

        if (to.getExtInfo() != null) {
            to.getExtInfo().forEach((k, v) -> {
                v = ComfyUIUtils.parseJsonObj(k, v);
                to.addExtInfo(k, v);
            });
        }

        //以下兼容处理
        if (StringUtils.equals(to.getConfigKey(), "FACE") && to.getExtInfo(KEY_FACE_RESTORE_VISIBILITY) == null) {
            to.addExtInfo(KEY_FACE_RESTORE_VISIBILITY, "0.7");
        }

        if (to.getExtInfo() != null && to.getExtInfo().containsKey(CommonConstants.KEY_OPEN_SCOPE)
            && !StringUtils.equals(to.getExtInfo(CommonConstants.KEY_OPEN_SCOPE, String.class), CommonConstants.ALL)) {
            to.setExclusive(true);
        }

        to.setPrivatelyOpen2UserId(from.getPrivatelyOpen2UserId());
        to.setPrivatelyOpen2UserNick(from.getPrivatelyOpen2UserNick());

        if (StringUtils.isNotBlank(from.getPrivatelyOpen2UserRoleType())) {
            to.setPrivatelyOpen2UserRoleType(RoleTypeEnum.getByCode(from.getPrivatelyOpen2UserRoleType()));
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static CreativeElementDO vo2DO(CreativeElementVO from) {
        CreativeElementDO to = new CreativeElementDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setConfigKey(from.getConfigKey());
        to.setLevel(from.getLevel());
        to.setParentId(from.getParentId());
        to.setShowImage(from.getShowImage());
        to.setStatus(from.getStatus() != null ? from.getStatus().getCode() : null);
        to.setOrder(from.getOrder());
        to.setMemo(from.getMemo());
        if (CollectionUtils.isNotEmpty(from.getType())) {
            to.setType(String.join(",", from.getType()));
        }
        to.setBelong(from.getBelong() != null ? from.getBelong().getCode() : null);
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTags(ComfyUIUtils.revertJsonStr(from.getTags()));
        to.setExtTags(ComfyUIUtils.revertJsonStr(from.getExtTags()));
        to.setIsNew(from.getIsNew());

        to.setLoraModelId(from.getLoraModelId());

        if (from.getExtInfo() != null) {
            from.getExtInfo().forEach((k, v) -> {
                v = v == null ? null : ComfyUIUtils.revertJsonObj(k, v);
                from.addExtInfo(k, v);
            });
        }

        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toJSONString() : null);

        return to;
    }

    /**
     * DO -> Query
     */
    public static CreativeElementQuery do2Query(CreativeElementDO from) {
        CreativeElementQuery to = new CreativeElementQuery();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setConfigKey(from.getConfigKey());
        to.setLevel(from.getLevel());
        to.setParentId(from.getParentId());
        to.setShowImage(from.getShowImage());
        to.setOrder(from.getOrder());
        to.setMemo(from.getMemo());
        to.setType(from.getType());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTags(from.getTags());
        to.setExtTags(from.getExtTags());
        to.setExtInfo(from.getExtInfo());
        to.setLoraModelId(from.getLoraModelId());
        to.setIsNew(from.getIsNew());

        return to;
    }

    /**
     * Query -> DO
     */
    public static CreativeElementDO query2DO(CreativeElementQuery from) {
        CreativeElementDO to = new CreativeElementDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setConfigKey(from.getConfigKey());
        to.setLevel(from.getLevel());
        to.setParentId(from.getParentId());
        to.setShowImage(from.getShowImage());
        to.setOrder(from.getOrder());
        to.setMemo(from.getMemo());
        to.setType(from.getType());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTags(from.getTags());
        to.setExtTags(from.getExtTags());
        to.setExtInfo(from.getExtInfo());
        to.setLoraModelId(from.getLoraModelId());
        to.setIsNew(from.getIsNew());

        return to;
    }

    /**
     * Query -> Example
     */
    public static CreativeElementExample query2Example(CreativeElementQuery from) {
        CreativeElementExample to = new CreativeElementExample();
        Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getIds())) {
            c.andIdIn(from.getIds());
        }
        if (!ObjectUtils.isEmpty(from.getStartCreateTime())) {
            c.andCreateTimeGreaterThanOrEqualTo(from.getStartCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getIdsOrParents())) {
            c.andIdOrParentIn(from.getIdsOrParents());
        }
        if (!ObjectUtils.isEmpty(from.getBelong())) {
            c.andBelongEqualTo(from.getBelong());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getConfigKey())) {
            c.andConfigKeyEqualTo(from.getConfigKey());
        }
        if (!ObjectUtils.isEmpty(from.getIsNew())) {
            c.andIsNewEqualTo(from.getIsNew());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeIncludes(from.getType());
        }

        if (!ObjectUtils.isEmpty(from.getTypeNotEqual())) {
            c.andTypeExcludes(from.getTypeNotEqual());
        }

        if (!ObjectUtils.isEmpty(from.getLoraModelId())) {
            c.andLoraModelIdEqualTo(from.getLoraModelId());
        }

        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }

        if (!ObjectUtils.isEmpty(from.getSwapType())) {
            c.andSwapTypeEqualTo(from.getSwapType());
        }

        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameLike(from.getName());
        }

        if (!ObjectUtils.isEmpty(from.getLevel())) {
            c.andLevelEqualTo(from.getLevel());
        }

        if (!ObjectUtils.isEmpty(from.getLevels())) {
            c.andLevelIn(from.getLevels());
        }

        if (from.isTrainFinished()) {
            c.andTrainFinished(BACKGROUND_SCENE_IDS);
        }

        if (from.isFaceOnlyLora()) {
            c.andIsFaceOnlyLora();
        }

        // 是否只展示含有展示图片的
        if (from.getIsHasShowImage()) {
            if (StringUtils.equalsIgnoreCase("public", from.getTempOpenScope())) {
                c.andTempOpenScopePublic();
            } else if (StringUtils.equalsIgnoreCase("private", from.getTempOpenScope())) {
                c.andTempOpenScopePrivate(from.getDistributorCustomerIds());
            } else {
                c.andTempOpenScopeEqualTo(from.getTempOpenScope());
            }
        }

        if (from.isRelatedToMe()) {
            c.andRelatedToUser(OperationContextHolder.getOperatorUserId());
        }

        // 未构建
        if (from.isNotBuild()) {
            c.andIsSetStyleEqualTo();
        }

        if (!ObjectUtils.isEmpty(from.getLabelType())) {
            c.andExtValueEquals(KEY_LABEL_TYPE, from.getLabelType());
        }

        if (!ObjectUtils.isEmpty(from.getLabelTypeNotEqual())) {
            c.andExtValueNotEquals(KEY_LABEL_TYPE, from.getLabelTypeNotEqual());
        }

        if (!ObjectUtils.isEmpty(from.getTrainType())) {
            c.andTrainTypeEqualTo(from.getTrainType());
        }

        if (!ObjectUtils.isEmpty(from.getIsLora())) {
            if (from.getIsLora()) {
                c.andIsLora();
            } else {
                c.andIsNotLora();
            }
        }

        if (!ObjectUtils.isEmpty(from.getGenderType())) {
            //male-model female-model
            c.andTypeIncludes(from.getGenderType());
        }

        if (!ObjectUtils.isEmpty(from.getGenderTypes())) {
            //male-model female-model
            c.andTypeOrIncludes(from.getGenderTypes());
        }

        if (!ObjectUtils.isEmpty(from.getStyleTypes())) {
            //风格标签
            c.andTypeOrIncludes(from.getStyleTypes());
        }

        // ageRange
        if (!ObjectUtils.isEmpty(from.getAgeRange())) {
            // 如果包含child则正常筛选
            if (from.getAgeRange().contains("child")) {
                c.andTypeOrIncludes(Collections.singletonList(from.getAgeRange()));
            } else if (from.getAgeRange().equals("teenager")) {
                c.andTypeOrIncludes(Collections.singletonList("teenager"));
            } else {
                // 不包含child则说明是成人'big-child', 'medium-child', 'small-child','teenager'
                c.andTypeExcludes(
                    Arrays.asList("big-child", "medium-child", "small-child", "teenager", "infant-child"));
            }
        }

        //  filterAgeRange 过滤条件（用于后台模糊查询，与上面的ageRange不会同时出现）
        if (!ObjectUtils.isEmpty(from.getFilterAgeRange())) {
            if (from.getFilterAgeRange().contains("adult")) {
                c.andTypeIncludes(from.getFilterAgeRange());
            } else {
                // "big-child", "medium-child", "small-child" 为三种儿童类型，
                // child-model 为童模标识（兼容旧数据）
                c.andTypeOrIncludes(CHILD_MODEL_TYPES);
            }
        }

        if (!ObjectUtils.isEmpty(from.getAgeRanges())) {
            c.andTypeOrIncludes(from.getAgeRanges());
        }

        if (CollectionUtils.isNotEmpty(from.getClothCategory())) {
            c.andExtValueIncludesAny(KEY_CLOTH_CATEGORY, from.getClothCategory());
        }

        if (!ObjectUtils.isEmpty(from.getOpenScope())) {
            if (StringUtils.equalsIgnoreCase("public", from.getOpenScope())) {
                c.andOpenScopePublic();
            } else if (StringUtils.equalsIgnoreCase("private", from.getOpenScope())) {
                c.andOpenScopePrivate(from.getDistributorCustomerIds());
            } else if (StringUtils.equalsIgnoreCase("distributor_all", from.getOpenScope())) {
                // 渠道商查询所有场景：公共场景 + 自己客户的专属场景
                c.andOpenScopePublicOrPrivateByDistributor(from.getDistributorCustomerIds());
            } else {
                c.andOpenScopeEqualTo(from.getOpenScope());
            }
        }

        if (from.isNeedInitClothTypeScope()) {
            c.andExtValueIsBlank(KEY_SCENE_CLOTH_TYPE_SCOPE);
        }

        if (from.isNeedInitClothCategory()) {
            c.andExtValueIsBlank(KEY_CLOTH_CATEGORY);
            c.andExtValueIsBlank(KEY_CLOTH_CATEGORY_OTHER);
        }

        if (from.isNeedInitStyleType()) {
            // 查询 isSetStyleType 不为 'Y' 的记录（包括为 'N' 或未设置的情况）
            // 使用 NOT EQUALS 'Y' 来实现，因为我们要排除已设置为 'Y' 的记录
            c.andExtValueNotEquals(CommonConstants.KEY_IS_SET_STYLE_TYPE, YES);
            c.andLoraModelIdIsNotNull();
        }

        if (from.isHasClothTypeScope()) {
            c.andExtValueIsNotBlank(KEY_SCENE_CLOTH_TYPE_SCOPE);
            c.andExtValueNotEquals(KEY_SCENE_CLOTH_TYPE_SCOPE, "[]");
        }

        if (from.isStyleScene()) {
            c.andExtValueEquals(KEY_IS_LORA, YES);
            c.andIdNotIn(BACKGROUND_SCENE_IDS);
        }

        if (!ObjectUtils.isEmpty(from.getScopeUserId())) {
            c.andOpenScopePublicOrEquals(from.getScopeUserId());
        }

        if (StringUtils.isBlank(from.getCreativeType())) {
            CreativeTypeEnum creativeType = CreativeTypeEnum.getByCode(from.getCreativeType());
            if (creativeType != null && creativeType.isExternal()) {
                c.andConfigKeyIn(Arrays.stream(creativeType.getConfigKeys()).map(ElementConfigKeyEnum::name)
                    .collect(Collectors.toList()));
            }
        }

        if (!ObjectUtils.isEmpty(from.getTypes())) {
            c.andTypeIncludes(from.getTypes());
        }

        if (!ObjectUtils.isEmpty(from.getTypesOr())) {
            c.andTypeOrIncludes(from.getTypesOr());
        }

        if (!ObjectUtils.isEmpty(from.getTypesOrList())) {
            for (List<String> typeOr : from.getTypesOrList()) {
                if (ObjectUtils.isEmpty(typeOr)) {
                    continue;
                }
                c.andTypeOrIncludes(typeOr);
            }
        }

        if (!ObjectUtils.isEmpty(from.getBodyTypes())) {
            c.andTypeOrIncludes(from.getBodyTypes());
        }

        if (!ObjectUtils.isEmpty(from.getPositions())) {
            c.andTypeOrIncludes(from.getPositions());
        }
        if (!ObjectUtils.isEmpty(from.getExcludesTypes())) {
            c.andTypeExcludes(from.getExcludesTypes());
        }

        if (from.isOnlyNearingDelivery()) {
            c.andStatusEqualTo(ElementStatusEnum.TEST.getCode());
            c.andUserTypeIsCustomer();

            //创建时间在 20 小时前，并且创建时间在 1 周内
            c.andCreateTimeLessThan(DateUtils.addHours(new Date(), -20));
            c.andCreateTimeGreaterThan(DateUtils.addWeeks(new Date(), -1));
        }

        //专属开放的账号类型
        if (StringUtils.isNotBlank(from.getPrivatelyOpen2UserRoleType())) {
            c.andPrivatelyOpen2UserRoleTypeEqualTo(from.getPrivatelyOpen2UserRoleType());
        }

        //专属开放的账号
        if (from.getPrivatelyOpen2UserId() != null) {
            c.andPrivatelyOpen2UserIdEqualTo(from.getPrivatelyOpen2UserId());
        }

        if (!ObjectUtils.isEmpty(from.getConfigKeys())) {
            c.andConfigKeyIn(from.getConfigKeys());
        }

        // 仅查看实验标签
        if (from.getOnlyExperimental() != null) {
            if (from.getOnlyExperimental()) {
                c.andIsExperimental();
            } else {
                c.andIsNotExperimental();
            }
        }

        if (from.getOnlyNoshowFace() != null && from.getOnlyNoshowFace()) {
            c.andExtValueEquals(KEY_NOSHOW_FACE, YES);
        }

        if (!ObjectUtils.isEmpty(from.getExcludesParentOrIds())) {
            c.andIdNotIn(from.getExcludesParentOrIds());
            c.andParentIdNotIn(from.getExcludesParentOrIds());
        }

        // 是否查询含有子数据的值
        if (from.getHasChildren() != null && from.getHasChildren()) {
            c.andHasChildren();
        }

        c.andDeletedEqualTo(false);

        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<CreativeElementVO> doList2VOList(List<CreativeElementDO> list) {
        return CommonUtil.listConverter(list, CreativeElementConverter::do2VO);
    }

    /**
     * vo list to level list
     */
    public static List<CreativeElementVO> toListByLevel(List<CreativeElementVO> list) {
        return toListByLevel(list, 1, null);
    }

    /**
     * 递归方式遍历生成树
     *
     * @param list     列表
     * @param level    登记
     * @param parentId 父节点id
     * @return 结果
     */
    public static List<CreativeElementVO> toListByLevel(List<CreativeElementVO> list, Integer level, Integer parentId) {
        List<CreativeElementVO> current = list.stream().filter(
            vo -> vo.getLevel() != null && vo.getLevel().equals(level) && (parentId == null || parentId.equals(
                vo.getParentId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(current)) {
            return new ArrayList<>();
        }

        current = current.stream().sorted(Comparator.comparing(CreativeElementVO::getOrder)).collect(
            Collectors.toList());

        current.forEach(vo -> {
            vo.setChildren(toListByLevel(list, level + 1, vo.getId()));
        });

        return current;
    }

    /**
     * 从文件加载场景
     *
     * @param file   文件
     * @param parent 父节点
     * @return 节点
     */
    public static CreativeElementVO loadFromFile(FileVO file, CreativeElementVO parent) {
        if (!StringUtils.equals(file.getType(), CommonConstants.KEY_JSON)) {
            return null;
        }
        LabelTypeEnum labelType = LabelTypeEnum.getByCode(parent.getExtInfo(KEY_LABEL_TYPE, String.class));
        if (labelType == LabelTypeEnum.STRUCTURAL) {
            return buildStructuralScene(file, parent);
        }

        JSONObject content = JSONObject.parseObject(file.getTextContent());
        if (content == null || !content.containsKey("desc")) {
            log.error("resetScene error,file={},id={}", file, parent.getId());
            return null;
        }

        StyleScene styleScene = content.getJSONObject("desc").toJavaObject(StyleScene.class);
        log.info("resetScene,id={},file={},content={}", parent.getId(), file.getFileName(),
            content.getJSONObject("desc"));

        SceneDetail scene = styleScene.getScene();

        CreativeElementVO child = new CreativeElementVO();
        List<String> types = new ArrayList<>(parent.getType());
        String characterOrientation = scene.getCharacterOrientation();
        //兼容side view
        CameraAngleEnum orientation = StringUtils.equals(characterOrientation, "side view") ? CameraAngleEnum.FRONT_VIEW
            : CameraAngleEnum.getByCode(characterOrientation, CameraAngleEnum.FRONT_VIEW);
        CameraAngleEnum view = CameraAngleEnum.getByCode(scene.getCharacterView(), CameraAngleEnum.WHOLE_BODY);
        if (orientation != null) {
            types.add(orientation.getCode());
        }
        if (view != null) {
            types.add(view.getCode());
        }

        child.setType(types);
        //构图背景
        child.setTags(styleScene.getActivateKey() + CommonUtil.formatTextWithComma(scene.getComposition()));
        //人物姿势 + 人物动作
        child.addExtInfo(KEY_POSTURE,
            CommonUtil.formatTextWithComma(scene.getAction()) + CommonUtil.formatTextWithComma(scene.getPose()));
        child.addExtInfo(KEY_LENS,
            CommonUtil.formatTextWithComma(characterOrientation) + CommonUtil.formatTextWithComma(
                scene.getCharacterView()));
        //人物搭配
        child.addExtInfo(KEY_STYLE_OUTFIT, JSONObject.toJSONString(styleScene.getOutfit()));
        //模特信息
        child.addExtInfo(KEY_STYLE_MODEL, JSONObject.toJSONString(styleScene.getModel()));

        return child;
    }

    /**
     * 从文件中加载一个主图信息转换成child
     *
     * @param labelFiles 打标文件
     * @param parent     父节点
     * @return 子节点
     */
    public static List<CreativeElementVO> loadMainFromFile(List<FileVO> labelFiles, CreativeElementVO parent) {
        if (CollectionUtils.isEmpty(labelFiles)) {
            log.warn("获取主图信息失败，打标文件为空，labelFiles={},id={}", labelFiles, parent.getId());
            return null;
        }

        LabelTypeEnum labelType = LabelTypeEnum.getByCode(parent.getExtInfo(KEY_LABEL_TYPE, String.class));
        if (LabelTypeEnum.DEFAULT == labelType) {
            log.warn("获取主图信息失败，当前训练方式属于默认打标，labelType={},id={}",
                parent.getExtInfo(KEY_LABEL_TYPE, String.class), parent.getId());
            return null;
        }

        List<CreativeElementVO> result = new ArrayList<>();
        String fileType = labelType == LabelTypeEnum.MINI ? CommonConstants.KEY_TEXT : CommonConstants.KEY_JSON;
        //String fileType = CommonConstants.KEY_TEXT;
        Map<CameraAngleEnum, String> cameraAngleMap = new HashMap<>();

        for (FileVO file : labelFiles) {
            if (!StringUtils.equals(file.getType(), fileType)) {
                continue;
            }

            CreativeElementVO child = null;

            if (labelType == LabelTypeEnum.STRUCTURAL) {
                child = buildStructuralFace(parent, file, labelFiles, cameraAngleMap);
            } else {
                child = new CreativeElementVO();

                if (CollectionUtils.isNotEmpty(parent.getType())) {
                    Set<String> typeSet = new HashSet<>(parent.getType());
                    CameraAngleEnum.removeAllAngle(typeSet);
                    child.setType(new ArrayList<>(typeSet));
                } else {
                    child.setType(new ArrayList<>());
                }
                child.getType().add(CameraAngleEnum.FRONT_VIEW.getCode());
                child.setLevel(3);
                child.setParentId(parent.getId());
                child.setConfigKey(parent.getConfigKey());

                String content = file.getTextContent();
                String tags = StringUtils.substringBefore(content, ",");

                child.setTags(tags);
                if (StringUtils.split(content, ",").length > 1) {
                    child.addExtInfo(KEY_HAIRSTYLE, StringUtils.substringAfter(content, ","));
                }
            }

            if (child == null) {
                continue;
            }

            result.add(child);

            //保持兼容，mini只返回一个即可
            if (labelType != LabelTypeEnum.STRUCTURAL) {
                return result;
            }
        }

        log.warn("获取主图信息成功，labelFiles={},id={}", labelFiles, parent.getId());
        return result;
    }

    /**
     * 构建结构化的模特信息
     *
     * @param parent         父节点
     * @param file           打标文件
     * @param labelFiles     打标文件列表
     * @param cameraAngleMap 角度map
     * @return 子节点模特信息
     */
    private static CreativeElementVO buildStructuralFace(CreativeElementVO parent, FileVO file, List<FileVO> labelFiles,
                                                         Map<CameraAngleEnum, String> cameraAngleMap) {
        String content = file.getTextContent();
        JSONObject json = JSONObject.parseObject(content);
        ImageAnalysisCaption analysis = json.toJavaObject(ImageAnalysisCaption.class);

        AssertUtil.assertNotNull(analysis, "获取结构化模特信息失败，打标文件内容为空，file=" + file);

        ViewpointEnum viewpoint = ViewpointEnum.getByCode(analysis.getLensLanguage().getViewpoint(),
            ViewpointEnum.NONE);

        CameraAngleEnum cameraAngle = viewpoint.getCameraAngle();

        if (StringUtils.isNotBlank(cameraAngleMap.get(cameraAngle))) {
            log.info("构建结构化模特信息，当前角度已存在，直接跳过，angle={},file={}", cameraAngle, file);
            return null;
        }

        cameraAngleMap.put(cameraAngle, file.getFileName());

        CreativeElementVO child = new CreativeElementVO();

        if (CollectionUtils.isNotEmpty(parent.getType())) {
            Set<String> typeSet = new HashSet<>(parent.getType());
            CameraAngleEnum.removeAllAngle(typeSet);
            child.setType(new ArrayList<>(typeSet));
        } else {
            child.setType(new ArrayList<>());
        }
        child.getType().add(cameraAngle.getCode());
        child.setLevel(3);
        child.setParentId(parent.getId());
        child.setConfigKey(parent.getConfigKey());

        String fileName = StringUtils.substringBeforeLast(file.getFileName(), ".");
        FileVO txtFile = labelFiles.stream().filter(f -> StringUtils.equals(f.getFileName(), fileName + ".txt"))
            .findFirst().orElse(null);

        child.setTags(txtFile != null ? txtFile.getTextContent() : null);
        child.addExtInfo(KEY_HAIRSTYLE, analysis.getModel().getHairstyle());
        child.addExtInfo(KEY_EXPRESSION, analysis.getModel().getFacialExpression());
        child.addExtInfo(KEY_AGE_DESC, analysis.getModel().getAge());

        child.addExtInfo(KEY_ANALYSIS_JSON, content);

        return child;
    }

    /**
     * 基于结构化结果填充模型信息
     *
     * @param file   内容
     * @param parent 父节点
     * @return 子节点
     */
    private static CreativeElementVO buildStructuralScene(FileVO file, CreativeElementVO parent) {
        JSONObject content = JSONObject.parseObject(file.getTextContent());
        if (content == null) {
            log.error("转换结构化分析结果失败，json解析失败,file={},id={}", file, parent.getId());
            return null;
        }

        ImageAnalysisCaption analysis = content.toJavaObject(ImageAnalysisCaption.class);
        if (analysis == null) {
            log.error("转换结构化分析结果失败，结果为空,file={},id={}", file, parent.getId());
            return null;
        }

        LensLanguage lensLanguage = analysis.getLensLanguage();
        ShootingTheme shootingTheme = analysis.getShootingTheme();

        CreativeElementVO child = new CreativeElementVO();
        List<String> types = new ArrayList<>(parent.getType());
        ViewpointEnum viewpoint = ViewpointEnum.getByCode(lensLanguage.getViewpoint(), ViewpointEnum.NONE);
        CompositionEnum composition = CompositionEnum.getByCode(lensLanguage.getComposition(), CompositionEnum.NONE);
        //兼容side view
        CameraAngleEnum orientation = viewpoint.getCameraAngle();
        CameraAngleEnum view = composition.getCameraAngle();
        if (orientation != null) {
            types.add(orientation.getCode());
        }
        if (view != null) {
            types.add(view.getCode());
        }

        child.setType(types);
        //构图背景
        child.setTags(shootingTheme.getShootingScene() + "," + shootingTheme.getOverlayElements());
        //人物姿势 + 人物动作
        child.addExtInfo(KEY_POSTURE, analysis.getModel().getPosture());
        if (orientation != null && view != null) {
            child.addExtInfo(KEY_LENS,
                CommonUtil.formatTextWithComma(orientation.getCode()) + CommonUtil.formatTextWithComma(view.getCode()));
        }

        child.addExtInfo(KEY_STYLE, shootingTheme.getGenre() + "," + shootingTheme.getPostProcessingStyle());

        //原始json
        child.addExtInfo(KEY_ANALYSIS_JSON, content);

        return child;
    }

}