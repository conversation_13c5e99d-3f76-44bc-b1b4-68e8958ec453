package ai.conrain.aigc.platform.service.model.vo;

import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RevertByVideoBatchVO {
    /** 需要退点的视频列表 */
    @NotEmpty
    private List<String> revertList;
    /** 批次id */
    @NotNull
    private Integer batchId;
    /** 退点备注 */
    private String revertMemo;
}
