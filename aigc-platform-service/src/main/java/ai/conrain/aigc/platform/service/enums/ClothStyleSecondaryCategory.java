package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

public interface ClothStyleSecondaryCategory {
    String getCode();

    String getDesc();

    ClothStyleFirstCategory getCategory();

    // 外套类枚举
    @Getter
    enum 外套类型 implements ClothStyleSecondaryCategory {
        西装外套("Suit_Jacket", "西装外套", ClothStyleFirstCategory.外套),
        新中式外套("New_Chinese_Style", "新中式外套", ClothStyleFirstCategory.外套),
        开衫("Cardigan", "开衫", ClothStyleFirstCategory.外套),
        夹克("Jacket", "夹克", ClothStyleFirstCategory.外套),
        风衣("Windbreaker", "风衣", ClothStyleFirstCategory.外套),
        大衣("Coat", "大衣", ClothStyleFirstCategory.外套),
        其它外套("Others", "其它外套", ClothStyleFirstCategory.外套);

        private final String code;
        private final String desc;
        private final ClothStyleFirstCategory category;

        外套类型(String code, String desc, ClothStyleFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }
    }

    // 冬季外套类枚举
    @Getter
    enum 冬季外套类型 implements ClothStyleSecondaryCategory {
        羽绒服("Down_Jacket", "羽绒服", ClothStyleFirstCategory.冬季外套),
        皮草("Fur", "皮草", ClothStyleFirstCategory.冬季外套),
        派克大衣("Parka", "派克大衣", ClothStyleFirstCategory.冬季外套),
        棉服("Cotton_Coat", "棉服", ClothStyleFirstCategory.冬季外套),
        其它冬季外套("Others", "其它冬季外套", ClothStyleFirstCategory.冬季外套);

        private final String code;
        private final String desc;
        private final ClothStyleFirstCategory category;

        冬季外套类型(String code, String desc, ClothStyleFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }
    }

    // 上衣类枚举
    @Getter
    enum 上衣类型 implements ClothStyleSecondaryCategory {
        新中式上衣("New_Chinese_Style_Top", "新中式上衣", ClothStyleFirstCategory.上衣),
        卫衣("Hoodie", "卫衣", ClothStyleFirstCategory.上衣),
        毛衣("Sweater", "毛衣", ClothStyleFirstCategory.上衣),
        短袖T恤("Short_Sleeve_T-shirt", "短袖T恤", ClothStyleFirstCategory.上衣),
        吊带("Camisole", "吊带", ClothStyleFirstCategory.上衣),
        打底衫("Base_Layer", "打底衫", ClothStyleFirstCategory.上衣),
        衬衫("Shirt", "衬衫", ClothStyleFirstCategory.上衣),
        T恤("T-shirt", "T恤", ClothStyleFirstCategory.上衣),
        POLO衫("Polo_Shirt", "POLO衫", ClothStyleFirstCategory.上衣),
        其它上衣("Others", "其它上衣", ClothStyleFirstCategory.上衣);

        private final String code;
        private final String desc;
        private final ClothStyleFirstCategory category;

        上衣类型(String code, String desc, ClothStyleFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }
    }

    // 半裙类枚举
    @Getter
    enum 半裙类型 implements ClothStyleSecondaryCategory {
        新中式半裙("New_Chinese_Style_Skirt", "新中式半裙", ClothStyleFirstCategory.半裙),
        其它半裙("Others", "其它半裙", ClothStyleFirstCategory.半裙);

        private final String code;
        private final String desc;
        private final ClothStyleFirstCategory category;

        半裙类型(String code, String desc, ClothStyleFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }
    }

    // 裤子类枚举
    @Getter
    enum 裤子类型 implements ClothStyleSecondaryCategory {
        运动裤("Sports_Pants", "运动裤", ClothStyleFirstCategory.裤子),
        西装裤("Dress_Pants", "西装裤", ClothStyleFirstCategory.裤子),
        牛仔裤("Jeans", "牛仔裤", ClothStyleFirstCategory.裤子),
        工装裤("Cargo_Pants", "工装裤", ClothStyleFirstCategory.裤子),
        短裤("Shorts", "短裤", ClothStyleFirstCategory.裤子),
        登山裤("Hiking_Pants", "登山裤", ClothStyleFirstCategory.裤子),
        打底裤("Leggings", "打底裤", ClothStyleFirstCategory.裤子),
        其它裤子("Others", "其它裤子", ClothStyleFirstCategory.裤子);

        private final String code;
        private final String desc;
        private final ClothStyleFirstCategory category;

        裤子类型(String code, String desc, ClothStyleFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }
    }

    // 连衣裙类枚举
    @Getter
    enum 连衣裙类型 implements ClothStyleSecondaryCategory {
        新中式连衣裙("New_Chinese_Style_Dress", "新中式连衣裙", ClothStyleFirstCategory.连衣裙),
        公主裙("Princess_Dress", "公主裙", ClothStyleFirstCategory.连衣裙),
        其它连衣裙("Others", "其它连衣裙", ClothStyleFirstCategory.连衣裙);

        private final String code;
        private final String desc;
        private final ClothStyleFirstCategory category;

        连衣裙类型(String code, String desc, ClothStyleFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }
    }

    // 套装类枚举
    @Getter
    enum 套装类型 implements ClothStyleSecondaryCategory {
        职业套装("Business_suit", "职业套装", ClothStyleFirstCategory.套装),
        西装套装("Business_Attire", "西装套装", ClothStyleFirstCategory.套装),
        休闲套装("Matching_Set", "休闲套装", ClothStyleFirstCategory.套装),
        新中式套装("New_Chinese_Style_Suit", "新中式套装", ClothStyleFirstCategory.套装),
        汉服("Hanfu", "汉服", ClothStyleFirstCategory.套装),
        其它套装("Others", "其它套装", ClothStyleFirstCategory.套装);

        private final String code;
        private final String desc;
        private final ClothStyleFirstCategory category;

        套装类型(String code, String desc, ClothStyleFirstCategory category) {
            this.code = code;
            this.desc = desc;
            this.category = category;
        }
    }
}
