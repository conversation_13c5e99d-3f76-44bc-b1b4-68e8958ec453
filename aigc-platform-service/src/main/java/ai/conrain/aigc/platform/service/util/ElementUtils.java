/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_IMAGE_MORE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_LORA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LENS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LORA_SWAP_FACE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_NOSHOW_FACE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SWAP_MODEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SWAP_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.actualSwapFaceImg;

/**
 * 创作元素工具类
 *
 * <AUTHOR>
 * @version : ElementUtils.java, v 0.1 2024/12/27 00:23 renxiao.wu Exp $
 */
@Slf4j
public abstract class ElementUtils {
    public final static List<Integer> BACKGROUND_SCENE_IDS = Arrays.asList(1981, 1713, 1528, 1509);

    /** 童模标签列表 */
    public final static List<String> CHILD_MODEL_TYPES = Arrays.asList("big-child", "medium-child", "small-child",
        "child-model");

    /**
     * 是否风格场景
     *
     * @param element 创作元素
     * @return true风格场景
     */
    public static boolean isStyleScene(CreativeElementVO element) {
        return element != null && StringUtils.equals(element.getConfigKey(), ElementConfigKeyEnum.SCENE.name())
               && StringUtils.equals(element.getExtInfo(KEY_IS_LORA, String.class), YES)
               && !BACKGROUND_SCENE_IDS.contains(element.getId()) && !BACKGROUND_SCENE_IDS.contains(
            element.getParentId());
    }

    /**
     * 是否lora模特
     *
     * @param element 创作元素
     * @return true lora模特
     */
    public static boolean isLoraFace(CreativeElementVO element) {
        return element != null && StringUtils.equals(element.getConfigKey(), ElementConfigKeyEnum.FACE.name())
               && StringUtils.equals(element.getExtInfo(KEY_SWAP_TYPE, String.class), "LORA");
    }

    /**
     * 是否不展示人脸
     *
     * @param element 场景元素
     * @return true不展示人脸
     */
    public static boolean isNoshowFace(CreativeElementVO element) {
        return isStyleScene(element) && StringUtils.equals(element.getExtInfo(KEY_NOSHOW_FACE, String.class), YES);
    }

    /**
     * 调整type
     *
     * @param element 元素
     */
    public static void correctType(CreativeElementVO element) {
        // 1.对type去重
        List<String> type = new ArrayList<>(new HashSet<>(element.getType()));

        // 2.朝向处理
        if (ElementUtils.isStyleScene(element)) {
            String lens = element.getExtInfo(KEY_LENS, String.class);
            if (StringUtils.isNotBlank(lens)) {
                // lens的值如"side view,upper body,"，按","进行split并去除空字符串，转换成list
                List<String> lensList = Arrays.stream(lens.split(",")).filter(StringUtils::isNotBlank).collect(
                    Collectors.toList());

                CameraAngleEnum.replaceBodyPosition(CameraAngleEnum.getBodyPositionByStr(lensList), type);
                CameraAngleEnum.replaceOrientation(CameraAngleEnum.getOrientationByStr(lensList), type);
            }
            log.info("当前风格场景重置角度，参数完成调整parentId={},id={},type={}", element.getParentId(),
                element.getId(), type);
        } else {
            log.info("当前非风格场景，直接跳过parentId={},id={},type={}", element.getParentId(), element.getId(), type);
        }

        element.setType(type);
    }

    /**
     * 调整type
     *
     * @param parent   元素
     * @param children 子元素
     */
    public static void correctType(CreativeElementVO parent, List<CreativeElementVO> children) {
        if (parent == null || CollectionUtils.isEmpty(parent.getType())) {
            return;
        }

        // 1.对type去重
        Set<String> type = new HashSet<>(parent.getType());

        // 2.朝向处理
        if (ElementUtils.isStyleScene(parent) && CollectionUtils.isNotEmpty(children)) {
            // 清除所有的角度标签
            CameraAngleEnum.removeAllAngle(type);

            Map<String, Integer> typeTimes = new HashMap<>();

            for (CreativeElementVO child : children) {
                List<String> childType = CameraAngleEnum.getAllAngle(child.getType());
                for (String ct : childType) {
                    typeTimes.put(ct, typeTimes.getOrDefault(ct, 0) + 1);
                }
            }
            // 至少要2个及以上的图片或者children数量小于3时才添加
            typeTimes.forEach((k, v) -> {
                if (v > 1 || CollectionUtils.size(children) <= 3) {
                    type.add(k);
                }
            });

            log.info("当前风格场景从子元素中重置type，参数完成调整={}", type);
        }

        parent.setType(new ArrayList<>(type));
    }

    /**
     * 判断是否要走instantId流程
     *
     * @param face 模特配置
     * @return true, 走instantId流程
     */
    public static boolean isInstantIdFlow(CreativeElementVO face) {
        if (face == null) {
            return false;
        }

        // 必须要是lora模特
        if (!isLoraFace(face)) {
            return false;
        }

        // 且需要换脸
        if (!StringUtils.equals(YES, face.getExtInfo(KEY_LORA_SWAP_FACE, String.class))) {
            return false;
        }

        // 换脸类型为instantId
        if (StringUtils.equals("instantId", face.getExtInfo(KEY_SWAP_MODEL_TYPE, String.class))) {
            return true;
        }

        // 或者imageMore的图片数为2
        String faceImageMoreStr = face.getExtInfo(KEY_FACE_IMAGE_MORE, String.class);
        if (StringUtils.isNotBlank(faceImageMoreStr)) {
            JSONArray faceImageMore = JSON.parseArray(faceImageMoreStr);
            return faceImageMore.size() >= 2;
        }

        return false;
    }

    /**
     * 判断是否是儿童模特
     *
     * @param face 模特配置
     * @return true,  儿童模特
     */
    public static boolean isChildModel(CreativeElementVO face) {
        if (face == null) {
            return false;
        }
        List<String> type = face.getType();
        if (CollectionUtils.isEmpty(type)) {
            return false;
        }
        return CHILD_MODEL_TYPES.stream().anyMatch(type::contains);
    }

    /**
     * 根据模特层级确定要显示的模特ID
     *
     * @param face 模特配置
     * @return 模特ID
     */
    private static Integer determineFaceModelId(CreativeElementVO face) {
        // 二级模特：使用自身ID
        if (face.getLevel() == 2) {
            return face.getId();
        }

        // 非二级模特：优先使用父级ID，如果没有父级ID则使用自身ID
        return face.getParentId() != null ? face.getParentId() : face.getId();
    }

    /**
     * 检查模特是否完整
     *
     * @param face 模特配置
     */
    public static void checkFaceComplete(CreativeElementVO face) {
        AssertUtil.assertNotNull(face, "模特配置不能为空");

        boolean isSwapFace = isLoraFace(face) && StringUtils.equals(YES,
            face.getExtInfo(KEY_LORA_SWAP_FACE, String.class));
        if (isSwapFace) {
            // 根据模特层级确定要显示的模特ID
            Integer faceModelId = determineFaceModelId(face);

            // 获取换脸图片（首图）
            String actualSwapFaceImgUrl = face.getExtInfo(actualSwapFaceImg, String.class);

            // 换脸图片为空时抛出异常
            if (StringUtils.isBlank(actualSwapFaceImgUrl)) {
                log.error("当前模特为换脸模型，但换脸图片为空，请检查模型配置, faceModelId={},curr={},extInfo={}",
                    faceModelId, face.getId(), face.getExtInfo());

                AssertUtil.assertNotBlank(actualSwapFaceImgUrl, ResultCode.FACE_ILLEGAL_CONFIG,
                    "当前模特不可用，请检查配置");
            }
        }
    }

    /**
     * 获取风格场景的根图片路径
     *
     * @param ossUrl OSS图片URL
     * @return 根图片路径
     */
    public static String getRootStyleImagePath(String ossUrl) {
        if (StringUtils.isBlank(ossUrl)) {
            return null;
        }

        String rootPath = StringUtils.substringAfterLast(ossUrl, "/");
        rootPath = StringUtils.substringBefore(rootPath, ".");

        //兼容逻辑：
        //老版本是：RandomStringUtils.randomAlphabetic(3) + file.getFileName()
        //新版本是：RandomStringUtils.randomAlphabetic(10) + "-" + file.getFileName()
        return StringUtils.contains(rootPath, "-") ? StringUtils.substringAfter(rootPath, "-") : StringUtils.substring(
            rootPath, 3);
    }
}
