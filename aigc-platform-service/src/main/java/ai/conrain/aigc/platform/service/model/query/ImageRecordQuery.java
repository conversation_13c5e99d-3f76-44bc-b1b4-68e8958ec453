package ai.conrain.aigc.platform.service.model.query;

import ai.conrain.aigc.platform.dal.example.ImageRecordAggQuery;
import ai.conrain.aigc.platform.dal.pgsql.entity.MetadataFieldDO;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * ImageQuery
 *
 * @version ImageService.java v 0.1 2025-07-30 02:33:24
 */
@Data
public class ImageRecordQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 图像ID，自增主键 */
    private Integer id;

    private Integer cursor;

    /** 用户 ID */
    private Integer userId;

    /** 图像类型，用于区分服装图、风格示意图 */
    private String type;

    /** 图像元数据，JSON格式 */
    private String metadata;

    /** 图像元数据，查询字段值 */
    List<MetadataFieldDO> metadataFields;

    /** 是否配置服装 */
    private Boolean paired;

    /** 打标结果 */
    private String result;

    /** 聚合查询条件 */
    private List<ImageRecordAggQuery> agg;

    /** 标签 */
    private List<String> tags;

    /** 流派 */
    private String intendedUse;

    /**
     * 是否低质量
     */
    private String lowQuality;

    /**
     * 质量
     */
    private String quality;

    /** 所属人id_key */
    private String owner;

    /** 创建时间 */
    private Date createTime;

    /** 创建时间范围查询 - 开始时间 */
    private Date createTimeStart;

    /** 创建时间范围查询 - 结束时间 */
    private Date createTimeEnd;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;
}
