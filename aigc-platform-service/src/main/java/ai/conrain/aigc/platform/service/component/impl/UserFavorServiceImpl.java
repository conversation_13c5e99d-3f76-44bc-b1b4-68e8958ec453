package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.UserFavorDAO;
import ai.conrain.aigc.platform.dal.entity.UserFavorDO;
import ai.conrain.aigc.platform.dal.example.UserFavorExample;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.UserFavorService;
import ai.conrain.aigc.platform.service.enums.FavorTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.model.biz.FavorCreationModel;
import ai.conrain.aigc.platform.service.model.biz.FavorImageDetail;
import ai.conrain.aigc.platform.service.model.biz.FavorImageModel;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.UserFavorConverter;
import ai.conrain.aigc.platform.service.model.query.UserFavorQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.LoraOption;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserFavorVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IMAGE_INDEXES;

/**   
 * UserFavorService实现
 *
 * <AUTHOR>
 * @version UserFavorService.java v 0.1 2025-03-04 11:46:07
 */
@Slf4j
@Service
public class UserFavorServiceImpl implements UserFavorService {

	@Autowired
	private UserFavorDAO userFavorDAO;
	@Autowired
	private CreativeElementService creativeElementService;
    @Autowired
    private MaterialModelService materialModelService;
	@Autowired
	private CreativeBatchService creativeBatchService;
    @Autowired
    private BatchFillHelper batchFillHelper;

	@Override
	public UserFavorVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "[用户收藏]id不能为空");

		UserFavorDO data = userFavorDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return UserFavorConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "[用户收藏]id不能为空");

		int n = userFavorDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "[用户收藏]删除收藏失败, id: " + id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserFavorVO removeFavor(UserFavorVO userFavor) {
		// 参数非空校验
		AssertUtil.assertNotNull(userFavor, ResultCode.PARAM_INVALID, "收藏参数不能为空");
		AssertUtil.assertNotNull(userFavor.getType(), ResultCode.PARAM_INVALID, "收藏类型不能为空");
		AssertUtil.assertNotNull(userFavor.getItemId(), ResultCode.PARAM_INVALID, "收藏对象id不能为空");

		// 检查权限
		checkPermission(userFavor.getType(), userFavor.getItemId());

		// 是否存在
		UserFavorVO data = validateUserFavorWithBlobs(userFavor.getType(), userFavor.getItemId());
		// 用户收藏不存在, 直接返回 null
		if (ObjectUtils.isEmpty(data) ) {
			return null;
		}
		if (FavorTypeEnum.isCreationType(data.getType())) {
			// 图片 or 视频 类型特殊逻辑
			return removeFavorImage(data, userFavor);
		}

		int n = userFavorDAO.deleteByPrimaryKey(data.getId());
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除UserFavor失败");
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserFavorVO addFavor(UserFavorVO vo) {
		// 参数非空校验
		AssertUtil.assertNotNull(vo, ResultCode.PARAM_INVALID, "[用户收藏]收藏参数不能为空");
		AssertUtil.assertNotNull(vo.getType(), ResultCode.PARAM_INVALID, "[用户收藏]收藏类型不能为空");
		AssertUtil.assertNotNull(vo.getItemId(), ResultCode.PARAM_INVALID, "[用户收藏]收藏对象id不能为空");
		FavorTypeEnum type = vo.getType();
		// 检查权限
		checkPermission(type, vo.getItemId());
		// 尝试获取已有的收藏记录
		UserFavorVO data = validateUserFavorWithBlobs(type, vo.getItemId());

		// 处理图片类型特殊逻辑
		if (FavorTypeEnum.isCreationType(type) && data != null) {
			return insertFavorImage(data, vo);
		}

		if (!ObjectUtils.isEmpty(data)) {
			return data;
		}
		return insert(vo);
	}

	@Override
	public UserFavorVO insert(UserFavorVO vo) {
		// 参数非空校验
		AssertUtil.assertNotNull(vo, ResultCode.PARAM_INVALID, "[用户收藏]收藏参数不能为空");
		AssertUtil.assertNotNull(vo.getType(), ResultCode.PARAM_INVALID, "[用户收藏]收藏类型不能为空");
		AssertUtil.assertNotNull(vo.getItemId(), ResultCode.PARAM_INVALID, "[用户收藏]收藏对象id不能为空");

		//创建时间、修改时间兜底
		if (vo.getCreateTime() == null) {
			vo.setCreateTime(new Date());
		}

		if (vo.getModifyTime() == null) {
			vo.setModifyTime(new Date());
		}

		if (vo.getUserId() == null) {
			vo.setUserId(OperationContextHolder.getOperatorUserId());
		}

		if (vo.getOperatorId() == null) {
			vo.setOperatorId(OperationContextHolder.getOperatorUserId());
		}

		if (FavorTypeEnum.IMAGE.equals(vo.getType()) && vo.getModelId() == null) {
			CreativeBatchVO batch = creativeBatchService.selectById(vo.getItemId());
			AssertUtil.assertNotNull(batch, ResultCode.PARAM_INVALID, "[用户收藏]创建收藏失败, 当前创作批次不存在");
			vo.setModelId(batch.getModelId());
		}

		if (FavorTypeEnum.VIDEO.equals(vo.getType()) && vo.getModelId() == null) {
			try {
				CreativeBatchVO batch = creativeBatchService.selectById(vo.getItemId());
				if (Objects.isNull(batch.getModelId())) {
					String showImage = batch.getShowImage();
					Integer modelId = batchFillHelper.getTaskByUrl(showImage).getModelId();
					vo.setModelId(modelId);
				} else {
					vo.setModelId(batch.getModelId());
				}
			} catch (Exception e) {
				log.info("[用户收藏], 无法从当前创作批次获取 modelId", e);
			}
		}

		UserFavorDO data = UserFavorConverter.vo2DO(vo);
		int n = userFavorDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "[用户收藏]创建收藏失败");
		AssertUtil.assertNotNull(data.getId(), "[用户收藏]新建收藏返回id为空");
		vo.setId(data.getId());
		return vo;
	}

	@Override
	public void updateByIdSelective(UserFavorVO userFavor) {
		AssertUtil.assertNotNull(userFavor, ResultCode.PARAM_INVALID, "userFavor is null");
    	AssertUtil.assertTrue(userFavor.getId() != null, ResultCode.PARAM_INVALID, "userFavor.id is null");

		//修改时间必须更新
		userFavor.setModifyTime(new Date());
		UserFavorDO data = UserFavorConverter.vo2DO(userFavor);
		int n = userFavorDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新UserFavor失败，影响行数:" + n);
	}

	@Override
	public List<UserFavorVO> queryUserFavorList(UserFavorQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		UserFavorExample example = UserFavorConverter.query2Example(query);

		List<UserFavorDO> list = userFavorDAO.selectByExample(example);
			return UserFavorConverter.doList2VOList(list);
	}

	@Override
	public List<UserFavorVO> queryUserFavorListWithBlobs(UserFavorQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		UserFavorExample example = UserFavorConverter.query2Example(query);

		List<UserFavorDO> list = userFavorDAO.selectByExampleWithBLOBs(example);
		return UserFavorConverter.doList2VOList(list);
	}

	@Override
	public Long queryUserFavorCount(UserFavorQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		UserFavorExample example = UserFavorConverter.query2Example(query);
		long c = userFavorDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询用户收藏
	 */
	@Override
	public PageInfo<UserFavorVO> queryUserFavorByPage(UserFavorQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		query.setUserId(OperationContextHolder.getOperatorUserId());

		PageInfo<UserFavorVO> page = new PageInfo<>();

		UserFavorExample example = UserFavorConverter.query2Example(query);
		long totalCount = userFavorDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<UserFavorDO> list = userFavorDAO.selectByExample(example);
		page.setList(UserFavorConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

	@Override
	public UserFavorVO validateUserFavor(FavorTypeEnum type, Integer itemId) {
		AssertUtil.assertNotNull(type, ResultCode.PARAM_INVALID, "[用户收藏]收藏类型不能为空");
		AssertUtil.assertNotNull(itemId, ResultCode.PARAM_INVALID, "[用户收藏]收藏对象id不能为空");
		UserFavorQuery query = new UserFavorQuery();
		query.setUserId(OperationContextHolder.getOperatorUserId());
		query.setType(type.getCode());
		query.setItemId(itemId);

		List<UserFavorVO> list = queryUserFavorList(query);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		AssertUtil.assertTrue(list.size() <= 1, ResultCode.BIZ_FAIL, "[用户收藏]收藏数据异常");
		return list.get(0);
	}

	@Override
	public UserFavorVO validateUserFavorWithBlobs(FavorTypeEnum type, Integer itemId) {
		AssertUtil.assertNotNull(type, ResultCode.PARAM_INVALID, "[用户收藏]收藏类型不能为空");
		AssertUtil.assertNotNull(itemId, ResultCode.PARAM_INVALID, "[用户收藏]收藏对象id不能为空");
		UserFavorQuery query = new UserFavorQuery();
		query.setUserId(OperationContextHolder.getOperatorUserId());
		query.setType(type.getCode());
		query.setItemId(itemId);

		List<UserFavorVO> list = queryUserFavorListWithBlobs(query);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		AssertUtil.assertTrue(list.size() <= 1, ResultCode.BIZ_FAIL, "[用户收藏]收藏数据异常");
		return list.get(0);
	}


	@Override
	public PageInfo<FavorCreationModel> getFavorImg4ModelByPage(UserFavorQuery query) {
		AssertUtil.assertTrue( !(
					query == null ||
					query.getPageSize() == null ||
					query.getPageNum() == null ||
					query.getPageSize() < 1 ||
					query.getPageNum() < 1
				),
				ResultCode.PARAM_INVALID, "查询参数异常");
		AssertUtil.assertTrue(FavorTypeEnum.isCreationType(query.getType()), ResultCode.PARAM_INVALID, "[用户收藏]收藏类型错误");

		PageInfo<FavorCreationModel> page = new PageInfo<>();
		query.setUserId(OperationContextHolder.getOperatorUserId());

		UserFavorExample example = UserFavorConverter.query2Example(query);
		example.getOredCriteria().get(0).andModelIdIsNotNull();
		List<UserFavorDO> dos = userFavorDAO.select4ModelByExample(example);
		long totalCount = CollectionUtils.isEmpty(dos) ? 0 : dos.size();
		if (totalCount == 0 ) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}
		List<FavorCreationModel> models = dos.stream().map(UserFavorConverter::do2Model).collect(Collectors.toList());
		page.setList(models);
		page.setTotalCount(totalCount);
		page.setSize(CollectionUtils.size(models));
		page.setHasNextPage(totalCount > ( example.getOffset() + example.getRows()));

		return page;
	}

	@Override
	public FavorImageDetail queryFavorDetail(UserFavorQuery query) {
		Integer modelId = query.getModelId();
		AssertUtil.assertNotNull(modelId, ResultCode.PARAM_INVALID, "[用户收藏]modelId不能为空");
		AssertUtil.assertNotNull(FavorTypeEnum.isCreationType(query.getType()), ResultCode.PARAM_INVALID, "[用户收藏]收藏类型错误");

		FavorImageDetail detail = new FavorImageDetail();
		MaterialModelVO modelVO = materialModelService.selectById(modelId);
		detail.setModelId(modelId);
		detail.setModelName(!ObjectUtils.isEmpty(modelVO) ? modelVO.getName() : "");

		query.setUserId(OperationContextHolder.getOperatorUserId());
		if (StringUtils.isBlank(query.getOrderBy())) {
			query.setOrderBy("modify_time desc");
		}
		UserFavorExample example = UserFavorConverter.query2Example(query);
		List<UserFavorDO> list = userFavorDAO.selectByExampleWithBLOBs(example);

		detail.setImages(list.stream().flatMap(this::getImageModels).collect(Collectors.toList()));

		return detail;
	}

	@Override
	public List<FavorImageModel> queryFavorImgWithoutModelId(UserFavorQuery query) {
		AssertUtil.assertTrue( !(
				query == null ||
				query.getPageSize() == null ||
				query.getPageNum() == null ||
				query.getPageSize() < 1 ||
				query.getPageNum() < 1
			),
			ResultCode.PARAM_INVALID, "查询参数异常");
		AssertUtil.assertTrue(FavorTypeEnum.isCreationType(query.getType()), ResultCode.PARAM_INVALID, "[用户收藏]收藏类型错误");

		query.setUserId(OperationContextHolder.getOperatorUserId());
		query.setOrderBy("modify_time desc");
		UserFavorExample example = UserFavorConverter.query2Example(query);
		example.getOredCriteria().get(0).andModelIdIsNull();
		List<UserFavorDO> list = userFavorDAO.selectByExampleWithBLOBs(example);

        return list.stream().flatMap(this::getImageModels).collect(Collectors.toList());
	}

	@Override
	public List<LoraOption> getModels4Favor() {
		UserFavorExample example = new UserFavorExample();
		example.createCriteria()
				.andUserIdEqualTo(OperationContextHolder.getOperatorUserId())
				.andTypeIn(Arrays.asList(FavorTypeEnum.VIDEO.getCode(), FavorTypeEnum.IMAGE.getCode()))
				.andModelIdIsNotNull();
		List<UserFavorDO> list = userFavorDAO.select4Model(example);
		return list.stream().map(UserFavorConverter::do2LoraOpt).collect(Collectors.toList());
	}

	/**
	 * 检查权限
	 * @param type 收藏对象
	 * @param itemId 收藏对象ID
	 */
	private void checkPermission(FavorTypeEnum type, Integer itemId) {
		if (OperationContextHolder.getRoleType().isBackRole()) {
			return;
		}

		// 渠道商可以收藏商家的创作
		if (OperationContextHolder.getRoleType().equals(RoleTypeEnum.DISTRIBUTOR)) {
			return;
		}

		Integer itemUserId = null;
		switch (type) {
			case VIDEO:
			case IMAGE:
				CreativeBatchVO batchVO = creativeBatchService.selectById(itemId);
				AssertUtil.assertNotNull(batchVO, ResultCode.BIZ_FAIL,
						"[用户收藏]数据不存在");
				itemUserId = batchVO.getUserId();
				break;
			case MATERIAL:
				MaterialModelVO modelVO = materialModelService.selectById(itemId);
				AssertUtil.assertNotNull(modelVO, ResultCode.BIZ_FAIL,
						"[用户收藏]数据不存在");
				itemUserId =modelVO.getUserId();
				break;
			// 元素暂时不作校验
			case ELEMENT:
				/*CreativeElementVO elementVO = creativeElementService.selectById(itemId);
				AssertUtil.assertNotNull(elementVO, ResultCode.BIZ_FAIL,
						"[用户收藏]数据不存在");
				AssertUtil.assertTrue(elementVO.getLevel().equals(2), ResultCode.BIZ_FAIL, "[用户收藏]权限校验失败");
				if (elementVO.getBelong().equals(ModelTypeEnum.SYSTEM)) {
					log.info("[用户收藏]系统资源不需要权限校验");
					return;
				}
				itemUserId = elementVO.getUserId();*/
				return;
			default:
				break;
		}

		AssertUtil.assertOperatePermission(itemUserId, "[用户收藏]权限校验失败");
	}

	/**
	 * 已有收藏记录时, 添加图片到用户收藏
	 *
	 * @param data 已有的收藏记录
	 * @param vo   要添加的记录
	 */
	private UserFavorVO insertFavorImage(UserFavorVO data, UserFavorVO vo) {
		List<Integer> images = vo.getExtInfo().getJSONArray(KEY_IMAGE_INDEXES).toJavaList(Integer.class);
		AssertUtil.assertNotEmpty(images, ResultCode.PARAM_INVALID, "添加用户收藏图片参数无效");

		JSONObject extInfo = data.getExtInfo();
		AssertUtil.assertNotNull(extInfo, ResultCode.PARAM_INVALID, "用户收藏数据异常");
		AssertUtil.assertNotEmpty(extInfo.getJSONArray(KEY_IMAGE_INDEXES), ResultCode.PARAM_INVALID, "用户收藏数据异常");
		List<Integer> list = extInfo.getJSONArray(KEY_IMAGE_INDEXES).toJavaList(Integer.class);
		AssertUtil.assertNotEmpty(list, ResultCode.PARAM_INVALID, "用户收藏数据异常");
		list.addAll(images);
		list = list.stream().distinct().collect(Collectors.toList());
		extInfo.put(KEY_IMAGE_INDEXES, list);
		data.setExtInfo(extInfo);
		// 必须更新时间
		data.setCreateTime(new Date());

		updateByIdSelective(data);

		return data;
	}

	/**
	 * 删除用户收藏图片
	 * @param data 已有收藏记录
	 * @param vo 要删除的记录
	 */
	private UserFavorVO removeFavorImage(UserFavorVO data, UserFavorVO vo) {

		List<Integer> images = vo.getExtInfo().getJSONArray(KEY_IMAGE_INDEXES).toJavaList(Integer.class);
		AssertUtil.assertNotEmpty(images, ResultCode.PARAM_INVALID, "添加用户收藏图片参数无效");

		JSONObject extInfo = data.getExtInfo();
		AssertUtil.assertNotNull(extInfo, ResultCode.PARAM_INVALID, "[用户收藏]数据异常");
		List<Integer> list = extInfo.getJSONArray(KEY_IMAGE_INDEXES).toJavaList(Integer.class);
		AssertUtil.assertNotEmpty(list, ResultCode.PARAM_INVALID, "[用户收藏]数据异常");

		boolean result = list.removeIf(images::contains);
		AssertUtil.assertTrue(result, ResultCode.PARAM_INVALID,
				"[用户收藏]要删除的收藏不存在, type: " + vo.getType() + ", itemId: " + vo.getItemId());

		if (CollectionUtils.isEmpty(list)) {
			deleteById(data.getId());
			return null;
		}

		extInfo.put(KEY_IMAGE_INDEXES, list);
		data.setExtInfo(extInfo);
		// 必须更新时间
		data.setModifyTime(new Date());

		updateByIdSelective(data);
		return data;
	}

	/**
	 * 将收藏的图片索引转换为图片模型
	 * @param data 收藏记录
	 * @return 结果流
	 */
	private Stream<FavorImageModel> getImageModels (UserFavorDO data) {
		try {
			List<Integer> indexes = JSONObject.parseObject(data.getExtInfo()).getJSONArray(KEY_IMAGE_INDEXES).toJavaList(Integer.class);
			CreativeBatchVO creativeBatchVO = creativeBatchService.selectById(data.getItemId());
			if (ObjectUtils.isEmpty(creativeBatchVO)) {
				return Stream.empty();
			}
			List<String> batchImage = creativeBatchVO.getResultImages();
			if (CollectionUtils.isEmpty(batchImage)) {
				return Stream.empty();
			}
			Collections.reverse(indexes);
			return indexes.stream().map(index -> {
				// 防止 数组越界
				if (index >= batchImage.size()) {
					return null;
				}
				FavorImageModel imgModel = new FavorImageModel();
				imgModel.setBatchId(data.getItemId());
				imgModel.setType(data.getType());
				imgModel.setIndex(index);
				imgModel.setImage(batchImage.get(index));
				return imgModel;
			}).filter(Objects::nonNull);
		} catch (Exception e) {
			log.error("[用户收藏]图片索引转换异常", e);
			return Stream.empty();
		}
	}
}