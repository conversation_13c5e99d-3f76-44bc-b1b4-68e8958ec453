/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.integration.gpt.AIModel.GptResponse;
import ai.conrain.aigc.platform.integration.gpt.AIModel.Status;
import ai.conrain.aigc.platform.integration.gpt.OpenAIService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static ai.conrain.aigc.platform.service.constants.SystemConstants.IMAGE_IDENTIFY_FACE_PROMPT;

/**
 * 图片识别帮助类
 *
 * <AUTHOR>
 * @version : ImageIdentifyHelper.java, v 0.1 2025/8/31 11:02 renxiao.wu Exp $
 */
@Component
public class ImageIdentifyHelper {
    @Autowired
    private OpenAIService openAIService;
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 判断当前图片是否包含人脸
     *
     * @param imageUrl 目标图片地址
     * @return true，包含人脸
     */
    public boolean isImageHasFace(String imageUrl) {
        if (StringUtils.isBlank(imageUrl)) {
            return false;
        }
        String prompt = systemConfigService.queryValueByKey(IMAGE_IDENTIFY_FACE_PROMPT);
        if (StringUtils.isBlank(prompt)) {
            return false;
        }
        GptResponse res = openAIService.requestGpt(prompt, List.of(imageUrl));
        AssertUtil.assertTrue(res != null && res.getStatus() == Status.OK, ResultCode.SYS_ERROR, "GPT服务异常");

        return StringUtils.containsIgnoreCase(res.getText(), "YES");
    }
}
