package ai.conrain.aigc.platform.service.component.creative.async;

import javax.imageio.ImageIO;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.model.biz.MaskMergeModel;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.request.MaskMergeCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.FileUtils;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MERGED_IMAGE_COMFY_UI;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MERGED_IMAGE_OSS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MERGED_MASK_MODEL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_MASK_MODEL;

@Slf4j
@Service("maskMergeAsyncExecutor")
public class MaskMergeAsyncExecutor extends AbstractBatchToAsyncExecutor<MaskMergeCreativeRequest, String> {

    @Autowired
    private OssService ossService;

    @Autowired
    private FileDispatch fileDispatch;

    @Override
    public boolean aboutTo(CreativeRequest request, CreativeBatchVO batch) {
        return request instanceof MaskMergeCreativeRequest;
    }

    @Override
    public String getOriginKey() {
        return KEY_ORIGIN_MASK_MODEL;
    }

    @Override
    public String getTransKey() {
        return KEY_MERGED_MASK_MODEL;
    }

    @Override
    protected Class<String> getModelClass() {
        return String.class;
    }

    @Override
    protected Object buildOriginValue(MaskMergeCreativeRequest request) {
        return JSONObject.toJSONString(request.getMaskMergeModels());
    }

    @Override
    protected String buildTransValue(CreativeTaskVO task, String origin) {
        List<MaskMergeModel> models = JSONObject.parseArray(origin, MaskMergeModel.class);
        models.forEach(model -> {
            boolean result = imageProcess(model, task);
            AssertUtil.assertTrue(result, ResultCode.BIZ_FAIL, "[异步合并蒙版]mask merge fail");
        });
        log.info("[异步合并蒙版]mask merge result:{}", models);
        return JSONArray.toJSONString(models);
    }

    @Override
    protected boolean aboutToExt() {
        return true;
    }

    @Override
    protected void fillExtInfo(String transValue, CreativeTaskVO task, List<CreativeElementVO> elements) {
        if (StringUtils.isBlank(transValue)) {
            return;
        }
        List<MaskMergeModel> models = JSONObject.parseArray(transValue, MaskMergeModel.class);
        models.forEach(model -> {
            String key = model.getKey();
            task.addExtInfo(KEY_MERGED_IMAGE_OSS + "_" + key, model.getMergedImageOss());
            task.addExtInfo(KEY_MERGED_IMAGE_COMFY_UI + "_" + key, model.getMergedImageComfyUi());
        });
    }

    /**
     * 把原图和蒙版合并
     */
    private boolean imageProcess(MaskMergeModel model, CreativeTaskVO task) {
        String image = model.getImage();
        String mask = model.getMask();
        try {
            // 1. 合并原始图片和蒙版
            // 1.1 从oss中获取原始图片
            String imageUrl = CommonUtil.getFilePathAndNameFromURL(image);
            String maskUrl = CommonUtil.getFilePathAndNameFromURL(mask);
            String originImageName = CommonUtil.getFileNameWithoutExtension(imageUrl);
            String originMaskName = CommonUtil.getFileNameWithoutExtension(maskUrl);
            String tmpUrl = ossService.downloadFile(imageUrl, "/tmp/", originImageName);
            log.info("[异步合并蒙版]图片下载成功, imageUrl={}", tmpUrl);
            String tmpMaskUrl = ossService.downloadFile(maskUrl, "/tmp/", originMaskName);
            log.info("[异步合并蒙版]蒙版下载成功, maskUrl={}", tmpMaskUrl);

            File originalImageFile = new File(tmpUrl);
            BufferedImage originalImageIO = ImageIO.read(originalImageFile);
            // 缩放，现在最大边为1920
            //originalImageIO = FileUtils.resize(originalImageIO, MAX_INPUT_IMAGE_SIZE);
            // 矫正图片方向
            originalImageIO = FileUtils.correctOrientation(originalImageIO, originalImageFile);
            BufferedImage maskImageIO = ImageIO.read(new File(tmpMaskUrl));
            // 1.2 合并图片和蒙版
            BufferedImage mergedImage = FileUtils.combine4DMask(originalImageIO, maskImageIO);
            log.info("[异步合并蒙版]图片和蒙版合并成功, image={}, mask={}", model.getImage(), model.getMask());
            // 2. 上传合并后的图片
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(mergedImage, "png", baos);
            byte[] imageBytes = baos.toByteArray();
            String imageName = originImageName + "_" + task.getBatchId() + "_" + model.getKey() + "_masked.png";
            // 2.1 上传到 oss
            ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes);
            String ossUrl = ossService.upload(imageName, bais);
            log.info("[异步合并蒙版]图片成功上传到oss, ossUrl={}", ossUrl);
            String md5 = CommonUtil.calculateMD5(imageBytes);
            log.info("[异步合并蒙版]md5={}", md5);
            // 2.2 上传到 comfyUI的input下
            String comfyUIPath = ComfyUIUtils.buildInputPath(task.getUserId()) + imageName;
            fileDispatch.uploadFile(comfyUIPath, new ByteArrayInputStream(imageBytes), ossUrl,
                task.getUserId(), false, null, md5);
            log.info("[异步合并蒙版]图片成功上传到comfyUI的input下, comfyUIPath={}", comfyUIPath);
            model.setMergedImageOss(ossUrl);
            model.setMergedImageComfyUi(comfyUIPath);
            return true;
        } catch (Exception e) {
            log.error("[异步合并蒙版]图片蒙版合并失败", e);
            return false;
        }
    }
}
