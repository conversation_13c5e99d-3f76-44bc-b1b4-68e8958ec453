package ai.conrain.aigc.platform.service.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.model.biz.MachineRoom;
import ai.conrain.aigc.platform.service.model.vo.ClothTypeScopeItem;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import ai.conrain.aigc.platform.service.model.vo.UserAdditionalConfigVO;
import java.math.BigDecimal;
import java.util.List;

/**
 * 系统配置 Service定义
 *
 * <AUTHOR>
 * @version SystemConfigService.java v 0.1 2024-01-20 01:21:37
 */
public interface SystemConfigService extends CachedService<SystemConfigVO, String, String> {

    /**
     * 查询系统配置对象
     *
     * @param id 主键
     * @return 返回结果
     */
    SystemConfigVO selectById(Integer id);

    /**
     * 删除系统配置对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加系统配置对象
     *
     * @param systemConfig 对象参数
     * @return 返回结果
     */
    SystemConfigVO insert(SystemConfigVO systemConfig);

    /**
     * 修改系统配置对象
     *
     * @param systemConfig 对象参数
     */
    void updateById(SystemConfigVO systemConfig);

    /**
     * 查询boolean型系统配置值
     *
     * @param key          key值
     * @param defaultValue 默认值
     * @return 系统配置值
     */
    boolean queryBoolValue(String key, boolean defaultValue);

    /**
     * 查询int型系统配置值
     *
     * @param key          key值
     * @param defaultValue 默认值
     * @return 系统配置值
     */
    int queryIntValue(String key, int defaultValue);

    /**
     * 获取BigDecimal型系统配置值
     *
     * @param key          key值
     * @param defaultValue 默认值
     * @return 系统配置值
     */
    BigDecimal queryBigDecimalValue(String key, BigDecimal defaultValue);

    /**
     * 查询long型系统配置值
     *
     * @param key          key值
     * @param defaultValue 默认值
     * @return 系统配置值
     */
    long queryLongValue(String key, long defaultValue);

    /**
     * 查询json型系统配置值
     *
     * @param key key值
     * @return 系统配置值
     */
    JSONObject queryJsonValue(String key);

    /**
     * 查询jsonArray型系统配置值
     *
     * @param key key值
     * @return 系统配置值
     */
    JSONArray queryJsonArrValue(String key);

    /**
     * 判断当前值是否在jsonArray中
     *
     * @param key   关键字
     * @param value 值
     * @return true，在列表中
     */
    boolean isInJsonArray(String key, Object value);

    /**
     * 根据用户id获取comfyui端口
     *
     * @param userId 用户id
     * @return comfyui端口
     */
    String queryComfyUIPort(Integer userId);

    /**
     * 根据用户id获取comfyui端口（A800 lora）
     *
     * @param userId          用户id
     * @param clothMaterialId
     * @param isLoraTask
     * @return comfyui端口
     */
    String queryComfyUILoraPort(Integer userId, Integer clothMaterialId, boolean isLoraTask);

    /**
     * 查询相同端口的用户列表
     *
     * @param userId 用户id
     * @return 相同端口的用户列表
     */
    List<Integer> querySamePortUser(Integer userId);

    /**
     * 查询所有相同端口的用户列表
     *
     * @return 相同端口的用户列表
     */
    List<List<Integer>> querySamePortUsers();

    /**
     * 查询内容安全禁止的标签列表
     *
     * @return 内容安全禁止的标签列表
     */
    List<String> queryTextModerationForbidLabel();

    /**
     * 查询内容安全禁止的标签列表
     *
     * @return 内容安全禁止的标签列表
     */
    List<String> queryModerationForbidLabel();

    /**
     * 获取分销商结算费率
     *
     * @param distributorCorpId 分销商id
     * @return 分销商结算费率
     */
    BigDecimal querySettleRate(Integer distributorCorpId);

    /**
     * 获取分销商结算费率配置
     *
     * @param distributorCorpId
     * @return
     */
    SystemConfigVO querySettleRateCfgByCorpId(Integer distributorCorpId);

    /**
     * 是否自动训练
     *
     * @param userId 用户id
     * @return true自动训练
     */
    boolean isAutoTrain(Integer userId);

    /**
     * 查询服装类型范围配置
     *
     * @return
     */
    List<ClothTypeScopeItem> queryClothTypeScopeCfg();

    /**
     * 查询所有设备信息
     *
     * @return 设备信息
     */
    List<MachineRoom> queryDeviceInfo();

    /**
     * 更新设备信息
     *
     * @param rooms 设备信息
     */
    void updateDeviceInfo(List<MachineRoom> rooms);

    /**
     * 根据端口编码查询服务url
     *
     * @param portId   端口编码
     * @param isPublic 是否公网
     * @return 服务url
     */
    String queryServerUrlByPort(String portId, boolean isPublic);

    /**
     * 判断当前时间是否已经触发白名单
     *
     * @param key 白名单KEY
     * @return true, 命中白名单
     */
    boolean isCurrTimeHitWhiteList(String key);

    /**
     * 查询用户额外需求配置
     *
     * @return 用户额外需求配置
     */
    UserAdditionalConfigVO queryAdditionalCustomerRequirements();

    /**
     * 查询绘蛙配置信息
     *
     * @param keyName      key 名称
     * @param defaultValue 当key不存在时的默认值
     * @return 绘蛙配置值（存在则返回实际值，否则返回默认值）
     */
    <T> T queryHuiWaConfig(String keyName, T defaultValue);

    /**
     * 计算当前客户最大创作并发数
     *
     * @return 客户最大创作并发数
     */
    int calcMaxPerCustomer();
}