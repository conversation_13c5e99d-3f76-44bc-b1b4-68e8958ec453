package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.GalleryDO;
import ai.conrain.aigc.platform.service.enums.GallerySubTypeEnum;
import ai.conrain.aigc.platform.service.enums.GalleryTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.model.query.GalleryQuery;
import ai.conrain.aigc.platform.dal.example.GalleryExample;
import ai.conrain.aigc.platform.service.model.vo.GalleryVO;

import com.alibaba.fastjson.JSONObject;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * GalleryConverter
 *
 * @version GalleryService.java v 0.1 2025-08-20 02:49:20
 */
public class GalleryConverter {

    /**
     * DO -> VO
     */
    public static GalleryVO do2VO(GalleryDO from) {
        GalleryVO to = new GalleryVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setType(GalleryTypeEnum.getByCode(from.getType()));
        to.setSubType(GallerySubTypeEnum.getByCode(from.getSubType()));
        to.setImageUrl(from.getImageUrl());
        to.setMd5(from.getMd5());
        to.setBelong(ModelTypeEnum.getByCode(from.getBelong()));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(CommonUtil.parseObject(from.getExtInfo()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static GalleryDO vo2DO(GalleryVO from) {
        GalleryDO to = new GalleryDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        if (!ObjectUtils.isEmpty(from.getType())) {
            to.setType(from.getType().getCode());
        }
        if (!ObjectUtils.isEmpty(from.getSubType())) {
            to.setSubType(from.getSubType().getCode());
        }
        to.setImageUrl(from.getImageUrl());
        to.setMd5(from.getMd5());
        if (!ObjectUtils.isEmpty(from.getBelong())) {
            to.setBelong(from.getBelong().getCode());
        }
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(CommonUtil.toJSONString(from.getExtInfo()));

        return to;
    }

    /**
     * Query -> Example
     */
    public static GalleryExample query2Example(GalleryQuery from) {
        GalleryExample to = new GalleryExample();
        GalleryExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getSubType())) {
            c.andSubTypeEqualTo(from.getSubType());
        }
        if (!ObjectUtils.isEmpty(from.getTagsIncludes())) {
            c.andTagsIncludes(from.getTagsIncludes());
        }
        if (!ObjectUtils.isEmpty(from.getTagsIncludesGroups())) {
            for (List<String> tagsIncludes : from.getTagsIncludesGroups()) {
                if (!ObjectUtils.isEmpty(tagsIncludes)) {
                    c.andTagsIncludes(tagsIncludes);
                }
            }
        }
        if (!ObjectUtils.isEmpty(from.getImageUrl())) {
            c.andImageUrlEqualTo(from.getImageUrl());
        }
        if (!ObjectUtils.isEmpty(from.getMd5())) {
            c.andMd5EqualTo(from.getMd5());
        }
        if (!ObjectUtils.isEmpty(from.getBelong())) {
            c.andBelongEqualTo(from.getBelong());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (GalleryExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        } else {
            to.setOrderByClause("modify_time desc");
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<GalleryVO> doList2VOList(List<GalleryDO> list) {
        return CommonUtil.listConverter(list, GalleryConverter::do2VO);
    }
}