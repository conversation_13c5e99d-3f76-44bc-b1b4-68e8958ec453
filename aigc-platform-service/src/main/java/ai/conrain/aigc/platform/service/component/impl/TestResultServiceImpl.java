package ai.conrain.aigc.platform.service.component.impl;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Collections;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.ImageCaseService;
import ai.conrain.aigc.platform.service.component.PromptDictService;
import ai.conrain.aigc.platform.service.enums.AnalysisTypeEnum;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.enums.ImageCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.query.ImageCaseQuery;
import ai.conrain.aigc.platform.service.model.vo.AnalysisRatioVO;
import ai.conrain.aigc.platform.service.model.vo.AnalysisRatioVO.TagStatistics;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.ExtInfoAnalysisRatioVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseVO;
import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;
import ai.conrain.aigc.platform.service.model.vo.ResultAnalysisRatioVO;
import ai.conrain.aigc.platform.service.model.vo.TestItemGroupVO;
import ai.conrain.aigc.platform.service.model.vo.ExtInfoReq;
import ai.conrain.aigc.platform.service.util.CustomCollectionUtils;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.TestResultDO;
import ai.conrain.aigc.platform.dal.example.TestResultExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.TestResultQuery;
import ai.conrain.aigc.platform.service.model.vo.TestResultVO;
import ai.conrain.aigc.platform.service.model.converter.TestResultConverter;
import ai.conrain.aigc.platform.dal.dao.TestResultDAO;
import ai.conrain.aigc.platform.service.component.TestResultService;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.CONTROL_GROUP_MAP;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.EXPERIMENTAL_GROUP_MAP;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.TOTAL_MAP;
import static ai.conrain.aigc.platform.service.model.converter.TestItemConverter.processTestItems;

/**
 * TestResultService实现
 *
 * <AUTHOR>
 * @version TestResultService.java v 0.1 2024-12-19 01:24:06
 */
@Slf4j
@Service
public class TestResultServiceImpl implements TestResultService {

    /** DAO */
    @Autowired
    private TestResultDAO testResultDAO;

    @Autowired
    private PromptDictService promptDictService;

    @Autowired
    private ImageCaseService imageCaseService;

    @Override
    public TestResultVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        TestResultDO data = testResultDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return TestResultConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = testResultDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TestResult失败");
    }

    @Override
    public TestResultVO insert(TestResultVO testResult) {
        AssertUtil.assertNotNull(testResult, ResultCode.PARAM_INVALID, "testResult is null");
        AssertUtil.assertTrue(testResult.getId() == null, ResultCode.PARAM_INVALID, "testResult.id is present");

        // 创建时间、修改时间兜底
        if (testResult.getCreateTime() == null) {
            testResult.setCreateTime(new Date());
        }

        if (testResult.getModifyTime() == null) {
            testResult.setModifyTime(new Date());
        }

        TestResultDO data = TestResultConverter.vo2DO(testResult);
        Integer n = testResultDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TestResult失败");
        AssertUtil.assertNotNull(data.getId(), "新建TestResult返回id为空");
        testResult.setId(data.getId());
        return testResult;
    }

    @Override
    public void updateByIdSelective(TestResultVO testResult) {
        AssertUtil.assertNotNull(testResult, ResultCode.PARAM_INVALID, "testResult is null");
        AssertUtil.assertTrue(testResult.getId() != null, ResultCode.PARAM_INVALID, "testResult.id is null");

        // 修改时间必须更新
        testResult.setModifyTime(new Date());
        TestResultDO data = TestResultConverter.vo2DO(testResult);
        int n = testResultDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TestResult失败，影响行数:" + n);
    }

    @Override
    public List<TestResultVO> queryTestResultList(TestResultQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestResultExample example = TestResultConverter.query2Example(query);

        List<TestResultDO> list = testResultDAO.selectByExample(example);
        return TestResultConverter.doList2VOList(list);
    }

    @Override
    public Long queryTestResultCount(TestResultQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestResultExample example = TestResultConverter.query2Example(query);
        long c = testResultDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询AB测试结果
     */
    @Override
    public PageInfo<TestResultVO> queryTestResultByPage(TestResultQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<TestResultVO> page = new PageInfo<>();

        TestResultExample example = TestResultConverter.query2Example(query);
        long totalCount = testResultDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<TestResultDO> list = testResultDAO.selectByExample(example);
        page.setList(TestResultConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initByTasks(List<CreativeTaskVO> tasks, TestItemGroupVO group) {
        List<TestResultDO> list = new ArrayList<>();
        tasks.forEach(task -> {
            TestResultVO result = TestResultConverter.task2Result(task, group);
            list.add(TestResultConverter.vo2DO(result));
        });

        testResultDAO.batchInsert(list);
    }

    @Override
    public ResultAnalysisRatioVO resultAnalysisRatio(Integer id, String type) {
        // 参数校验
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");
        AssertUtil.assertNotNull(type, ResultCode.PARAM_INVALID, "type is null");

        // 获取所有 BadCase 标签
        List<PromptDictVO> promptDictVOList = promptDictService.queryListByType(DictTypeEnum.IMAGE_TAGS,
            ImageCaseTypeEnum.BAD_CASE.getCode());
        if (CollectionUtils.isEmpty(promptDictVOList)) {
            log.error(
                "[BadCase 占用率分析]TestResultServiceImpl::resultAnalysisRatio::BadCase 标签不存在或查询出现异常...");
            return null;
        }

        // 获取分析数据类型
        AnalysisTypeEnum analysisTypeEnum = AnalysisTypeEnum.getByCode(type);
        if (analysisTypeEnum == null) {
            log.error("[BadCase 占用率分析]TestResultServiceImpl::resultAnalysisRatio::分析数据类型不存在...");
            return null;
        }

        return doAnalysisRatio(id, promptDictVOList, analysisTypeEnum);
    }

    
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateExtInfo(ExtInfoReq req) {
        // 参数校验
        AssertUtil.assertNotNull(req, ResultCode.PARAM_INVALID, "req is null");
        AssertUtil.assertNotNull(req.getId(), ResultCode.PARAM_INVALID, "id is null");

        // 参数提取
        Integer id = req.getId();
        String extInfoLabel = req.getExtInfoLabel();
        // 测试结果中该字段设置为 Integer 类型
        Integer extInfoValue = Integer.parseInt(String.valueOf(req.getExtInfoValue()));

        // 获取测试结果信息
        TestResultVO testResult = selectById(id);
        if (testResult == null) {
            log.error("[测试结果][更新扩展信息]TestResultServiceImpl::updateExtInfo::测试结果不存在...");
            return false;
        }

        // 获取扩展信息 （json 字符串）
        JSONObject extInfo = testResult.getExtInfo();
        // 如果扩展信息为空，则初始化
        if (extInfo == null) {
            extInfo = new JSONObject();
        }

        // 更新扩展信息
        extInfo.put(extInfoLabel, extInfoValue);

        // 设置扩展信息
        testResult.setExtInfo(extInfo);

        // 更新测试结果
        updateByIdSelective(testResult);

        // 返回结果
        return true;
    }



    /**
     * 分析BadCase 占用率
     *
     * @param id               测试项id(或其他 Id)
     * @param promptDictVOList BadCase 列表
     * @param analysisTypeEnum 分析数据类型
     * @return BadCase 占用率
     */
    private ResultAnalysisRatioVO doAnalysisRatio(Integer id, List<PromptDictVO> promptDictVOList,
                                                  AnalysisTypeEnum analysisTypeEnum) {
        // 初始化返回值
        ResultAnalysisRatioVO resultAnalysisRatioVO = new ResultAnalysisRatioVO();

        // badCaseMap 集合
        Map<String, List<Integer>> badCaseTagMap = new HashMap<>();
        // 子项 badCaseMap 集合(key:Id value：对应 badCase 集合)
        Map<Integer, Map<String, List<Integer>>> childBadCaseTagMap = new HashMap<>();

        // 分析数据类型
        switch (analysisTypeEnum) {
            case TEST_PLAN:
                childBadCaseTagMap = analysisRatioFormTestPlan(id, badCaseTagMap);
                break;
            case OTHER_INFO:
                // TODO 其他类型待完善
                break;
        }

        // 构建汇总结果
        resultAnalysisRatioVO.setResultAnalysisRatioVO(buildAnalysisRatio(badCaseTagMap, promptDictVOList));

        // 构建子项结果
        resultAnalysisRatioVO.setChildAnalysisRatioVOList(
            buildResultAnalysisRatioVOList(childBadCaseTagMap, promptDictVOList));

        // 返回结果
        return resultAnalysisRatioVO;
    }

    /**
     * 分析BadCase 占用率
     *
     * @param id            AB测试计划id
     * @param badCaseTagMap map集合
     */
    private Map<Integer, Map<String, List<Integer>>> analysisRatioFormTestPlan(Integer id,
                                                                               Map<String, List<Integer>> badCaseTagMap) {
        // 获取测试结果列表
        List<TestResultVO> testResultList = getTestResultList(id);
        if (CollectionUtils.isEmpty(testResultList)) {
            return null;
        }

        // 获取所有图片的 case 信息
        List<ImageCaseVO> imageCaseVOList = getImageCaseList(testResultList);
        if (CollectionUtils.isEmpty(imageCaseVOList)) {
            return null;
        }

        // 按分组处理数据
        Map<Integer, List<TestResultVO>> groupedMap = testResultList.stream()
            .collect(Collectors.groupingBy(TestResultVO::getGroupId));

        // 初始化实验组和对照组的图片列表
        List<ImageCaseVO> experimentalGroupImages = new ArrayList<>();
        List<ImageCaseVO> controlGroupImages = new ArrayList<>();

        // 分类图片到实验组和对照组
        for (Map.Entry<Integer, List<TestResultVO>> entry : groupedMap.entrySet()) {
            Integer groupId = entry.getKey();
            List<TestResultVO> groupResults = entry.getValue();

            // 获取该组的所有 caseId
            List<Integer> caseIds = groupResults.stream()
                .map(TestResultVO::getCaseId)
                .collect(Collectors.toList());

            // 找到对应的图片
            List<ImageCaseVO> groupImages = imageCaseVOList.stream()
                .filter(image -> caseIds.contains(image.getId()))
                .collect(Collectors.toList());

            // 根据 groupId 的奇偶性分配到对应组
            if (groupId % 2 != 0) {
                experimentalGroupImages.addAll(groupImages);
            } else {
                controlGroupImages.addAll(groupImages);
            }
        }

        // 填充总数据
        badCaseTagMap.put(TOTAL_MAP, CustomCollectionUtils.flatMapList(imageCaseVOList, ImageCaseVO::getTags));

        // 填充实验组数据
        if (!experimentalGroupImages.isEmpty()) {
            badCaseTagMap.put(EXPERIMENTAL_GROUP_MAP,
                CustomCollectionUtils.flatMapList(experimentalGroupImages, ImageCaseVO::getTags));
        }

        // 填充对照组数据
        if (!controlGroupImages.isEmpty()) {
            badCaseTagMap.put(CONTROL_GROUP_MAP,
                CustomCollectionUtils.flatMapList(controlGroupImages, ImageCaseVO::getTags));
        }

        // 根据 itemId 进行分组处理
        Map<Integer, List<TestResultVO>> groupedMapByItem = testResultList.stream()
            .collect(Collectors.groupingBy(TestResultVO::getItemId));

        // 处理每个测试项的数据
        return processTestItems(groupedMapByItem, imageCaseVOList);
    }

    /**
     * 获取测试结果列表
     *
     * @param id 测试计划ID
     * @return 测试结果列表
     */
    private List<TestResultVO> getTestResultList(Integer id) {
        TestResultQuery testResultQuery = new TestResultQuery();
        testResultQuery.setPlanId(id);
        List<TestResultVO> testResultList = queryTestResultList(testResultQuery);
        if (CollectionUtils.isEmpty(testResultList)) {
            log.info("[BadCase 占用率分析]TestResultServiceImpl::getTestResultList::AB测试计划结果为空或者查询失败...");
        }
        return testResultList;
    }

    /**
     * 获取图片案例列表
     *
     * @param testResultList 测试结果列表
     * @return 图片案例列表
     */
    private List<ImageCaseVO> getImageCaseList(List<TestResultVO> testResultList) {
        List<Integer> caseIdList = testResultList.stream()
            .map(TestResultVO::getCaseId)
            .collect(Collectors.toList());

        ImageCaseQuery query = new ImageCaseQuery();
        query.setCaseIdList(caseIdList);
        return imageCaseService.queryImageCaseList(query);
    }

    /**
     * 构建分析比率数据
     *
     * @param badCaseTagMap    badCase标签Map
     * @param promptDictVOList 标签字典列表
     * @return 分析比率VO
     */
    private AnalysisRatioVO buildAnalysisRatio(Map<String, List<Integer>> badCaseTagMap,
                                               List<PromptDictVO> promptDictVOList) {
        // 初始化比较结果
        AnalysisRatioVO analysisRatioVO = new AnalysisRatioVO();

        // 总比较结果标签 id 列表
        List<Integer> totalTagIdList = badCaseTagMap.get(TOTAL_MAP);
        // 实验组标签 id 列表
        List<Integer> experimentalGroupTagIdList = badCaseTagMap.get(EXPERIMENTAL_GROUP_MAP);
        // 对照组标签 id 列表
        List<Integer> controlGroupList = badCaseTagMap.get(CONTROL_GROUP_MAP);

        // 首先计算每个标签在总体中的出现次数
        Map<Integer, Long> tagCountMap = new HashMap<>();
        // 计算所有标签的总数
        long allTagsTotal = 0L;
        if (CollectionUtils.isNotEmpty(totalTagIdList)) {
            // 计算所有标签的总数
            allTagsTotal = totalTagIdList.size();

            // 计算每个标签的出现次数
            for (PromptDictVO promptDictVO : promptDictVOList) {
                long count = totalTagIdList.stream()
                    .filter(tagId -> tagId.equals(promptDictVO.getId()))
                    .count();
                tagCountMap.put(promptDictVO.getId(), count);
            }
        }

        // 总标签 id 列表不为空
        if (CollectionUtils.isNotEmpty(totalTagIdList)) {
            Map<String, TagStatistics> totalMap = new HashMap<>();
            calculateTagStatisticsMap(totalMap, totalTagIdList, promptDictVOList, tagCountMap, allTagsTotal, true);
            analysisRatioVO.setTotalMap(totalMap);
        }

        // 实验组标签 id 列表不为空
        if (CollectionUtils.isNotEmpty(experimentalGroupTagIdList)) {
            Map<String, TagStatistics> experimentalGroupMap = new HashMap<>();
            calculateTagStatisticsMap(experimentalGroupMap, experimentalGroupTagIdList, promptDictVOList, tagCountMap,
                allTagsTotal, false);
            analysisRatioVO.setExperimentalGroupMap(experimentalGroupMap);
        }

        // 对照组标签 id 列表不为空
        if (CollectionUtils.isNotEmpty(controlGroupList)) {
            Map<String, TagStatistics> controlGroupMap = new HashMap<>();
            calculateTagStatisticsMap(controlGroupMap, controlGroupList, promptDictVOList, tagCountMap,
                allTagsTotal, false);
            analysisRatioVO.setControlGroupMap(controlGroupMap);
        }

        return analysisRatioVO;
    }

    /**
     * 构建子测试结果分析结果列表
     *
     * @param childBadCaseTagMap 子测试结果分析结果
     * @param promptDictVOList   提示字典
     * @return 子测试结果分析结果列表
     */
    private List<AnalysisRatioVO> buildResultAnalysisRatioVOList(
        Map<Integer, Map<String, List<Integer>>> childBadCaseTagMap,
        List<PromptDictVO> promptDictVOList) {

        if (childBadCaseTagMap == null || childBadCaseTagMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 创建子项列表
        List<AnalysisRatioVO> childList = new ArrayList<>();

        // 遍历每个子项
        for (Map.Entry<Integer, Map<String, List<Integer>>> entry : childBadCaseTagMap.entrySet()) {
            Integer itemId = entry.getKey();
            Map<String, List<Integer>> childBadCaseTagMapItem = entry.getValue();

            // 为每个子项创建一个新的 AnalysisRatioVO
            AnalysisRatioVO childAnalysisRatioVO = buildAnalysisRatio(childBadCaseTagMapItem, promptDictVOList);
            // 设置子项ID
            childAnalysisRatioVO.setId(itemId);

            // 将子项添加到列表中
            childList.add(childAnalysisRatioVO);
        }

        // 按照 Id 排序
        childList.sort(Comparator.comparing(AnalysisRatioVO::getId));

        // 返回排序后的列表
        return childList;
    }

    /**
     * 计算标签统计信息
     *
     * @param tagStatisticsMap 标签统计Map
     * @param tagIdList        标签ID列表
     * @param promptDictVOList 标签字典列表
     * @param tagCountMap      标签计数Map
     * @param allTagsTotal     所有标签的总数
     * @param isTotal          是否是总计算（totalMap）
     */
    private void calculateTagStatisticsMap(Map<String, TagStatistics> tagStatisticsMap, List<Integer> tagIdList,
                                           List<PromptDictVO> promptDictVOList, Map<Integer, Long> tagCountMap,
                                           Long allTagsTotal, boolean isTotal) {
        // 遍历所有标签
        for (PromptDictVO promptDictVO : promptDictVOList) {
            TagStatistics tagStatistics = new TagStatistics();

            // 获取当前标签在总体中的出现次数
            Long totalCount = tagCountMap.getOrDefault(promptDictVO.getId(), 0L);

            // 计算当前标签在当前组中的出现次数
            long count = tagIdList.stream()
                .filter(tagId -> tagId.equals(promptDictVO.getId()))
                .count();

            // 如果是 totalMap，totalCount 设置为所有标签的总数
            if (isTotal) {
                tagStatistics.setTotalCount(allTagsTotal);
            } else {
                // 如果不是 totalMap，totalCount 设置为该标签在总体中的出现次数
                tagStatistics.setTotalCount(totalCount);
            }

            // 设置当前组中的数量
            tagStatistics.setCount(count);
            // 设置比例
            if (isTotal) {
                // 如果是 totalMap，计算该标签数量占所有标签总数的比例
                tagStatistics.setRatio(allTagsTotal > 0 ? (count * 100L / allTagsTotal) : 0L);
            } else {
                // 如果不是 totalMap，计算该标签在当前组中的数量占该标签总数的比例
                tagStatistics.setRatio(totalCount > 0 ? (count * 100L / totalCount) : 0L);
            }

            // 将统计信息添加到Map中
            tagStatisticsMap.put(promptDictVO.getWord(), tagStatistics);
        }
    }

}