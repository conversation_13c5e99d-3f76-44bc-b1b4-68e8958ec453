package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.PostgresExampleDO;
import ai.conrain.aigc.platform.dal.example.PostgresExampleExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.PostgresExampleQuery;
import ai.conrain.aigc.platform.service.model.vo.PostgresExampleVO;
import ai.conrain.aigc.platform.service.model.converter.PostgresExampleConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.PostgresExampleDAO;
import ai.conrain.aigc.platform.service.component.PostgresExampleService;

/**   
 * PostgresExampleService实现
 *
 * <AUTHOR>
 * @version PostgresExampleService.java v 0.1 2025-07-22 04:16:39
 */
@Slf4j
@Service
public class PostgresExampleServiceImpl implements PostgresExampleService {

	/** DAO */
	@Autowired
	private PostgresExampleDAO postgresExampleDAO;

	@Override
	public PostgresExampleVO selectById(Long id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		PostgresExampleDO data = postgresExampleDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return PostgresExampleConverter.do2VO(data);
	}

	@Override
	public void deleteById(Long id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = postgresExampleDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除PostgresExample失败");
	}

	@Override
	public PostgresExampleVO insert(PostgresExampleVO postgresExample) {
		AssertUtil.assertNotNull(postgresExample, ResultCode.PARAM_INVALID, "postgresExample is null");
		AssertUtil.assertTrue(postgresExample.getId() == null, ResultCode.PARAM_INVALID, "postgresExample.id is present");

		//创建时间、修改时间兜底
		if (postgresExample.getCreateTime() == null) {
			postgresExample.setCreateTime(new Date());
		}

		if (postgresExample.getModifyTime() == null) {
			postgresExample.setModifyTime(new Date());
		}

		PostgresExampleDO data = PostgresExampleConverter.vo2DO(postgresExample);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = postgresExampleDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建PostgresExample失败");
		AssertUtil.assertNotNull(data.getId(), "新建PostgresExample返回id为空");
		postgresExample.setId(data.getId());
		return postgresExample;
	}


	@Override
	public void updateByIdSelective(PostgresExampleVO postgresExample) {
		AssertUtil.assertNotNull(postgresExample, ResultCode.PARAM_INVALID, "postgresExample is null");
    	AssertUtil.assertTrue(postgresExample.getId() != null, ResultCode.PARAM_INVALID, "postgresExample.id is null");

		//修改时间必须更新
		postgresExample.setModifyTime(new Date());
		PostgresExampleDO data = PostgresExampleConverter.vo2DO(postgresExample);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = postgresExampleDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新PostgresExample失败，影响行数:" + n);
	}

	@Override
	public List<PostgresExampleVO> queryPostgresExampleList(PostgresExampleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		PostgresExampleExample example = PostgresExampleConverter.query2Example(query);

		List<PostgresExampleDO> list = postgresExampleDAO.selectByExample(example);
			return PostgresExampleConverter.doList2VOList(list);
	}

	@Override
	public Long queryPostgresExampleCount(PostgresExampleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		PostgresExampleExample example = PostgresExampleConverter.query2Example(query);
		long c = postgresExampleDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询示例表，存储用户基本信息
	 */
	@Override
	public PageInfo<PostgresExampleVO> queryPostgresExampleByPage(PostgresExampleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<PostgresExampleVO> page = new PageInfo<>();

		PostgresExampleExample example = PostgresExampleConverter.query2Example(query);
		long totalCount = postgresExampleDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<PostgresExampleDO> list = postgresExampleDAO.selectByExample(example);
		page.setList(PostgresExampleConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}