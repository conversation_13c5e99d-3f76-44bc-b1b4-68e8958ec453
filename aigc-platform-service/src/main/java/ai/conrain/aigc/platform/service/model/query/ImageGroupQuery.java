package ai.conrain.aigc.platform.service.model.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * ImageGroupQuery
 *
 * @version ImageGroupService.java v 0.1 2025-07-30 08:19:30
 */
@Data
public class ImageGroupQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Integer id;

    private String groupId;

    /** 图片ID列表 */
    private List<Integer> imageIds;

    /** 标签 */
    private String tag;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;


    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}
