package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.PostgresExampleQuery;
import ai.conrain.aigc.platform.service.model.vo.PostgresExampleVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * 示例表，存储用户基本信息 Service定义
 *
 * <AUTHOR>
 * @version PostgresExampleService.java v 0.1 2025-07-22 04:16:39
 */
public interface PostgresExampleService {
	
	/**
	 * 查询示例表，存储用户基本信息对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	PostgresExampleVO selectById(Long id);

	/**
	 * 删除示例表，存储用户基本信息对象
	 * @param id 主键
	 */
	void deleteById(Long id);

	/**
	 * 添加示例表，存储用户基本信息对象
	 * @param postgresExample 对象参数
	 * @return 返回结果
	 */
	PostgresExampleVO insert(PostgresExampleVO postgresExample);

	/**
	 * 修改示例表，存储用户基本信息对象
	 * @param postgresExample 对象参数
	 */
	void updateByIdSelective(PostgresExampleVO postgresExample);

	/**
	 * 带条件批量查询示例表，存储用户基本信息列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<PostgresExampleVO> queryPostgresExampleList(PostgresExampleQuery query);

	/**
	 * 带条件查询示例表，存储用户基本信息数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryPostgresExampleCount(PostgresExampleQuery query);

	/**
	 * 带条件分页查询示例表，存储用户基本信息
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<PostgresExampleVO> queryPostgresExampleByPage(PostgresExampleQuery query);
}