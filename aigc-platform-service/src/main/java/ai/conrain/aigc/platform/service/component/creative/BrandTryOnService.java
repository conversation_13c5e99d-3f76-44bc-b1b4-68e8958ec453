package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeBatchConverter;
import ai.conrain.aigc.platform.service.model.request.BrandTryOnRequest;
import ai.conrain.aigc.platform.service.model.request.ImageMaskModel;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonFlowUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service
public class BrandTryOnService extends AbstractCreativeService<BrandTryOnRequest> {

    @Autowired
    private BatchFillHelper batchFillHelper;

    @Override
    protected CreativeBatchVO buildData(BrandTryOnRequest request, MaterialModelVO modelVO) throws IOException {
        AssertUtil.assertNotEmpty(request.getReferenceImages(), "上传参考图为空");
        List<ImageMaskModel> referenceImages = request.getReferenceImages();
        CreativeBatchVO batch = CreativeBatchConverter.request2VO(request);
        List<String> images = new ArrayList<>();
        images.add(request.getClothImage());
        images.add(request.getClothMaskUrl());
        // 先 并行上传 clothImage 和 clothImageMask , 允许失败
        Map<String, String> uploadResult = batchFillHelper.uploadImagesParallel(images, OperationContextHolder.getMasterUserId());
        // 尝试获取原始task, 并获取原始图片
        // 服装图原图
        batch.addExtInfo(KEY_ORIGIN_IMAGE, request.getClothImage());
        batch.addExtInfo(KEY_CLOTH_ORIGIN_IMAGE, request.getClothImage());
        batch.addExtInfo(KEY_CLOTH_IMAGE_COMFY_UI, uploadResult.get(request.getClothImage()));
        // 服装图蒙版
        batch.addExtInfo(KEY_CLOTH_IMAGE_MASK, request.getClothMaskUrl());
        batch.addExtInfo(KEY_CLOTH_IMAGE_MASK_COMFY_UI, uploadResult.get(request.getClothMaskUrl()));
        log.info("[大牌上身]创建batch 上传服装图");

        batch.addExtInfo(KEY_REFERENCE_IMAGES, referenceImages);
        if (StringUtils.equals(HISTORY, request.getImageSource())) {
            batch.addExtInfo(KEY_IS_CREATIVE_UPLOAD, NO);
        } else {
            batch.addExtInfo(KEY_IS_CREATIVE_UPLOAD, YES);
        }
        return batch;
    }

    @Override
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        List<CreativeTaskVO> tasks =  super.buildTasks(batch, elements);
        // 更新一下 batch
        creativeBatchService.updateByIdSelective(batch);
        return tasks;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {
        List<ImageMaskModel> referenceImages = batch.getExtInfoList(KEY_REFERENCE_IMAGES, ImageMaskModel.class);
        AssertUtil.assertNotEmpty(referenceImages, ResultCode.BIZ_FAIL, "[大牌上身]参考图为空 referenceImages is empty");
        AssertUtil.assertTrue(referenceImages.size() > idx, ResultCode.BIZ_FAIL, "[大牌上身]缺少参考图 referenceImages size <= idx");
        // 根据索引 获取对应的 参考图片及其蒙版
        ImageMaskModel imageMaskModel = referenceImages.get(idx);
        AssertUtil.assertNotNull(imageMaskModel, ResultCode.BIZ_FAIL, "[大牌上身]参考图为空 imageMaskModel is null");
        List<String> images = new ArrayList<>();
        images.add(imageMaskModel.getImageUrl());
        images.add(imageMaskModel.getMaskUrl());

        // 检查 服装图及蒙版有没有上传成功
        String clothImageUrl = batch.getExtInfo(KEY_CLOTH_ORIGIN_IMAGE);
        String clothMaskUrl = batch.getExtInfo(KEY_CLOTH_IMAGE_MASK);
        String clothImageComfyUI = batch.getExtInfo(KEY_CLOTH_IMAGE_COMFY_UI);
        String clothMaskComfyUI = batch.getExtInfo(KEY_CLOTH_IMAGE_MASK_COMFY_UI);

        boolean needUploadCloth = StringUtils.isBlank(clothImageComfyUI) || StringUtils.isBlank(clothMaskComfyUI);

        // 如果之前没有上传成功, 再上传一次
        if (needUploadCloth) {
            images.add(clothImageUrl);
            images.add(clothMaskUrl);
            log.warn("[大牌上身]创建task 服装图再次上传 clothImageComfyUI or clothMaskComfyUI is blank, try again");
        }

        // 并行上传 参考图和蒙版
        Map<String, String> uploadResult = batchFillHelper.uploadImagesParallel(images, batch.getUserId());
        String imageComfyUI = uploadResult.get(imageMaskModel.getImageUrl());
        String maskComfyUI = uploadResult.get(imageMaskModel.getMaskUrl());
        AssertUtil.assertNotBlank(imageComfyUI, ResultCode.BIZ_FAIL, "[大牌上身]参考图上传失败 imageComfyUI is blank");
        AssertUtil.assertNotBlank(maskComfyUI, ResultCode.BIZ_FAIL, "[大牌上身]参考图蒙版上传失败 maskComfyUI is blank");

        if (needUploadCloth) {
            clothImageComfyUI = uploadResult.get(clothImageUrl);
            clothMaskComfyUI = uploadResult.get(clothMaskUrl);
            AssertUtil.assertNotBlank(clothImageComfyUI, ResultCode.BIZ_FAIL, "[大牌上身]参考图上传失败 clothImageComfyUI is blank");
            AssertUtil.assertNotBlank(clothMaskComfyUI, ResultCode.BIZ_FAIL, "[大牌上身]参考图蒙版上传失败 clothMaskComfyUI is blank");
        }
        // 保存上传结果到扩展字段
        // 单独存一下参考图
        target.addExtInfo(REFERENCE_ORIGINAL_IMAGE, imageMaskModel.getImageUrl());
        // 参考图
        target.addExtInfo(KEY_ORIGIN_IMAGE, imageMaskModel.getImageUrl());
        target.addExtInfo(KEY_ORIGIN_IMAGE_COMFY_UI, imageComfyUI);
        // 参考图蒙版
        target.addExtInfo(KEY_ORIGIN_IMAGE_MASK, imageMaskModel.getMaskUrl());
        target.addExtInfo(KEY_ORIGIN_IMAGE_MASK_COMFY_UI, maskComfyUI);

        // 服装图
        target.addExtInfo(KEY_CLOTH_ORIGIN_IMAGE, clothImageUrl);
        target.addExtInfo(KEY_CLOTH_IMAGE_COMFY_UI, clothImageComfyUI);
        // 服装图蒙版
        target.addExtInfo(KEY_CLOTH_IMAGE_MASK, clothMaskUrl);
        target.addExtInfo(KEY_CLOTH_IMAGE_MASK_COMFY_UI, clothMaskComfyUI);

        // 更新 batch
        batch.addExtInfo(KEY_CLOTH_IMAGE_COMFY_UI, clothImageComfyUI);
        batch.addExtInfo(KEY_CLOTH_IMAGE_MASK_COMFY_UI, clothMaskComfyUI);

        target.setBatchCnt(4);
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        context.put(KEY_ORIGIN_IMAGE_COMFY_UI, task.getExtInfo(KEY_ORIGIN_IMAGE_COMFY_UI));
        context.put(KEY_ORIGIN_IMAGE_MASK_COMFY_UI, task.getExtInfo(KEY_ORIGIN_IMAGE_MASK_COMFY_UI));
        context.put(KEY_CLOTH_IMAGE_COMFY_UI, task.getExtInfo(KEY_CLOTH_IMAGE_COMFY_UI));
        context.put(KEY_CLOTH_IMAGE_MASK_COMFY_UI, task.getExtInfo(KEY_CLOTH_IMAGE_MASK_COMFY_UI));
        context.put(KEY_GROW_MASK, task.getStringFromExtInfo(KEY_GROW_MASK));
        context.put(KEY_CFG, task.getStringFromExtInfo(KEY_CFG));
    }

    @Override
    public String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey) {
        return CommonFlowUtil.correctNumber(flow, context);
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.BRAND_TRY_ON;
    }

}
