package ai.conrain.aigc.platform.service.model.biz;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class VideoClipTask {

    //creative batch id
    private Integer id;

    //index: 0~3
    private Integer index;

    //tmp video url from kling
    private String outVideoUrl;

    //oss video url
    private String ossVideoUrl;

    //auto gen prompt
    private String prompt;

    //auto gen duration
    private Integer duration;

    //auto gen taskId
    private Integer taskId;

    //auto gen task status
    private String taskStatus;

    //auto gen outside task id (e.g., kling taskId)
    private String outTaskId;

    //auto gen outside task platform name (e.g., Kling)
    private String outTaskPlatform;

    // 退点成功标记
    private String refundMusePointCompleted;

    // 退点备注
    private String revertMemo;
    //其它任务运行参数，由CommonTaskVO同步过来，没有实际存储，只用于前端展示
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}