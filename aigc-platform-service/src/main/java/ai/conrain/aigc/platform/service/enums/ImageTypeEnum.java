/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 图片类型
 */
@Getter
@AllArgsConstructor
public enum ImageTypeEnum {
    SCENE("scene", "场景"),

    CLOTH("cloth", "服装"),

    TRYON("tryon", "tryon服装"),

    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static ImageTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (ImageTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }

}
