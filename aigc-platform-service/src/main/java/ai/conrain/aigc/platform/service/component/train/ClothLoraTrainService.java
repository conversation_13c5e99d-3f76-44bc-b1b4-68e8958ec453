/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.train;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.BizConstants;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.LoraActivateKeys;
import ai.conrain.aigc.platform.service.enums.CutoutTypeEnum;
import ai.conrain.aigc.platform.service.enums.LabelTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.helper.GrayscaleTestHelper;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.request.AddClothMaterialRequest;
import ai.conrain.aigc.platform.service.model.request.AddClothMaterialRequest.TrainParam;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.GrayscaleTestUtils;
import ai.conrain.aigc.platform.service.util.MaterialModelUtils;
import ai.conrain.aigc.platform.service.util.MaterialUploadUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.UserUtils;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LABEL_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.NO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.YES;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_LABEL_SWITCH;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_MINI_ACTIVATE_KEYS;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_MINI_LABEL_MERCHANT_LIST;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_NEW_LABEL_MERCHANT_LIST;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_TRAIN_EXT_INFO;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.NO_CUTOUT_MERCHANT_LIST;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.TEMP_SWITCH;

/**
 * 服装模型训练服务
 *
 * <AUTHOR>
 * @version : ClothLoraTrainService.java, v 0.1 2025/2/24 00:03 renxiao.wu Exp $
 */
@Slf4j
@Service
public class ClothLoraTrainService extends AbstractLoraTrainService<AddClothMaterialRequest> {

    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private CommonTaskService commonTaskService;
    @Autowired
    private GrayscaleTestHelper grayscaleTestHelper;
    @Autowired
    private UserService userService;

    @Override
    protected void precheck(AddClothMaterialRequest request) {
        boolean isVirtualMerchant = UserUtils.isVirtualMerchant(OperationContextHolder.getCurrentUser());

        if (OperationContextHolder.getRoleType() == RoleTypeEnum.DISTRIBUTOR && request.getPrincipalId() != null) {
            UserVO userVO = userService.selectById(request.getPrincipalId());
            isVirtualMerchant = UserUtils.isVirtualMerchant(userVO);
        }

        if (isVirtualMerchant) {
            AssertUtil.assertNotBlank(request.getUsageType(), ResultCode.ILLEGAL_USAGE_INFO,
                "当前请求未填写用途信息，请刷新页面后重试");
            if (StringUtils.equals("experience", request.getUsageType())) {
                AssertUtil.assertNotBlank(request.getUsageMemo(), ResultCode.ILLEGAL_USAGE_INFO,
                    "当前请求未填写用途信息，请刷新页面后重试");
            }
        }
    }

    @Override
    protected boolean needConsumePoint(MaterialModelVO materialModel) {
        return !OperationContextHolder.isBackRole();
    }

    @Override
    protected String getShowImage(AddClothMaterialRequest request) {
        //外部指定显示图片
        if (StringUtils.isNotBlank(request.getShowImage())) {
            return request.getShowImage();
        }
        return MaterialUploadUtil.getShowImage4Model(request.getMaterialDetail());
    }

    /**
     * 后置处理
     *
     * @param materialModel 模型
     * @param request       请求
     */
    @Override
    protected void postProcess(MaterialModelVO materialModel, AddClothMaterialRequest request) {
        super.postProcess(materialModel, request);

        //判断是否有自动交付的出图任务
        if (request.applyAutoGen()) {

            //创建common autoGenImgTask，记录自动交付图任务
            CommonTaskVO autoGenImgTask = materialModelService.createAutoGemImgTask(materialModel,
                request.getAutoGenImgParam());

            materialModel.getClothLoraTrainDetail().setAutoGenImg(true);
            materialModel.getClothLoraTrainDetail().setAutoGenImgParam(request.getAutoGenImgParam());
            materialModel.getClothLoraTrainDetail().setAutoGenImgTaskId(autoGenImgTask.getId());
            super.materialModelService.updateByIdSelective(materialModel);
        }
    }

    @Override
    protected void fillOtherTrainDetail(AddClothMaterialRequest request, LoraTrainDetail trainDetail,
                                        MaterialModelVO model) {
        Integer userId = model.getUserId();

        if (request.getMainType() != null) {
            model.setMainType(request.getMainType());
        }
        if (request.getMainId() != null) {
            model.setMainId(request.getMainId());
        }

        //解决多色拆分，背景色打标逻辑不一致问题
        if (StringUtils.isBlank(request.getBgMultiColor())) {
            if ((systemConfigService.isInJsonArray(TEMP_SWITCH, "ALL") || systemConfigService.isInJsonArray(TEMP_SWITCH,
                userId)) || grayscaleTestHelper.isHit("bgMultiColor", "服装训练-背景多色打标")) {
                trainDetail.setBgMultiColor(YES);
            } else {
                trainDetail.setBgMultiColor(NO);
            }
            request.setBgMultiColor(trainDetail.getBgMultiColor());
        } else {
            trainDetail.setBgMultiColor(request.getBgMultiColor());
        }

        trainDetail.setMultiColors(request.isMultiColors() ? "Y" : "N");
        trainDetail.setMatchPrefer(request.getMatchPrefer());

        if (CollectionUtils.isNotEmpty(request.getColorDescriptions())) {
            trainDetail.setColorDescriptions(JSONObject.toJSONString(request.getColorDescriptions()));
        }

        trainDetail.setClothType(MaterialUploadUtil.getClothType4TrainDetail(request.getClothType()));
        trainDetail.setOriginClothType(request.getClothType());

        trainDetail.setColorNumber(request.getColorNumber().toString());

        boolean noCutout = systemConfigService.isInJsonArray(NO_CUTOUT_MERCHANT_LIST, userId);
        if (noCutout) {
            log.info("命中不抠图商家白名单，将不进行抠图");
            trainDetail.setCutoutOnlyUpscale("Y");
        }
        trainDetail.setCutoutType(request.getCutoutType());
        if (StringUtils.isNotBlank(request.getReservedItems())) {
            trainDetail.setReservedItems(request.getReservedItems());
            CutoutTypeEnum cutoutType = StringUtils.equals(YES, request.getReservedItems())
                ? CutoutTypeEnum.CLOTH_AND_MANNEQUIN : CutoutTypeEnum.DEFAULT;
            trainDetail.setCutoutType(cutoutType.getCode());
        } else if (StringUtils.isNotBlank(request.getCutoutType())) { //兼容代码
            //TODO by半泉:
            log.info("兼容代码监控，看到这行日志说明还无法删除");
            String reservedItems = StringUtils.equals(CutoutTypeEnum.CLOTH_AND_MANNEQUIN.getCode(),
                request.getCutoutType()) ? YES : NO;
            trainDetail.setReservedItems(reservedItems);
        }

        if (StringUtils.equals(trainDetail.getCutoutType(), CutoutTypeEnum.CLOTH_AND_MANNEQUIN.getCode())) {
            trainDetail.setCutoutModel("sam_hq_vit_h (2.57GB)");
        }

        LabelTypeEnum labelType = LabelTypeEnum.getByCode(model.getExtInfo(KEY_LABEL_TYPE, String.class));
        labelType = labelType == null ? LabelTypeEnum.DEFAULT : labelType;
        trainDetail.setActivateKey(buildActivateKey(MaterialType.cloth.name(), labelType.getCode(), noCutout));
        trainDetail.setClothDetailsPrompt(buildClothLabelPrompt(labelType));

        // 设置训练扩展信息到训练流程中
        JSONObject extInfo = systemConfigService.queryJsonValue(LORA_TRAIN_EXT_INFO);
        String trainExtInfo = MaterialModelUtils.buildTrainExtInfo(trainDetail.getMaterialType(), extInfo);
        trainDetail.setTrainExtInfo(trainExtInfo);

        // 设置是否自动训练
        if (StringUtils.equals(BizConstants.TEST_CASE, model.getExtInfo(BizConstants.BIZ_TAG, String.class))) {
            trainDetail.setAutoTrain("Y");
        }

        // 设置训练参数信息
        TrainParam trainParam = request.getTrainParam();
        // 训练参数
        if (Objects.nonNull(trainParam)) {
            trainDetail.setLoraType(CommonConstants.FLUX);
            trainDetail.setResolution(trainParam.getResolution());
            trainDetail.setContentOrStyle(trainParam.getContentOrStyle());
            trainDetail.setRank(trainParam.getRank());
            trainDetail.setAlpha(trainParam.getAlpha());
            trainDetail.setDropout(trainParam.getDropout());
            trainDetail.setLr(trainParam.getLr());
            trainDetail.setMaxTrainStep(trainParam.getMaxTrainStep());
        }
        // 全量抠图1536
        //trainDetail.setImageSize(String.valueOf(1536));

        trainDetail.setCut4ScaleUp(YES);
    }

    @Override
    protected void fillMaterialInfoExt(JSONObject extInfo, AddClothMaterialRequest request) {
        extInfo.put(CommonConstants.KEY_CLOTH_NEED_BACK_IMG, request.isBackPhotoNeeded());
        extInfo.put(CommonConstants.KEY_CLOTH_STYLE_TYPE, request.getClothStyleType());
        extInfo.put(CommonConstants.KEY_BATCH_ID, request.getBatchId());
        extInfo.put("multiColors", request.isMultiColors() ? "Y" : "N");
        extInfo.put("colorNumber", request.getColorNumber());
        extInfo.put(CommonConstants.KEY_IRONING_CLOTH, request.isIroningCloth());
        extInfo.put("matchPrefer", request.getMatchPrefer());
        extInfo.put(CommonConstants.KEY_COLOR_DESCRIPTIONS, request.getColorDescriptions());
        extInfo.put(CommonConstants.KEY_AGE_RANGE, request.getAgeRange());
    }

    @Override
    protected void fillMaterialModelExt(JSONObject ext, AddClothMaterialRequest request, Integer userId) {
        ext.put(CommonConstants.KEY_BATCH_ID, request.getBatchId());
        ext.put(CommonConstants.clothStyleType,
            StringUtils.defaultIfBlank(request.getClothStyleType(), CommonConstants.female));
        ext.put(CommonConstants.KEY_IRONING_CLOTH, request.isIroningCloth() ? "Y" : "N");
        ext.put(CommonConstants.colorNumber, request.getColorNumber());
        ext.put(KEY_LABEL_TYPE, parseLabelType(userId, request.getLabelType()).getCode());
        ext.put(CommonConstants.KEY_AGE_RANGE, request.getAgeRange());

        // 渠道商代传
        if (request.getPrincipalId() != null && !request.getPrincipalId().equals(
            OperationContextHolder.getMasterUserId())) {
            log.info("渠道商代传服装,客户principalId={},渠道商currentLoginMasterId={}", request.getPrincipalId(),
                OperationContextHolder.getMasterUserId());
            ext.put(CommonConstants.uploadByAgentId, OperationContextHolder.getOperatorUserId());
            ext.put(CommonConstants.uploadByAgentName, OperationContextHolder.getOperatorNick());
            ext.put(CommonConstants.uploadByAgentMasterId, OperationContextHolder.getMasterUserId());
            ext.put(CommonConstants.uploadByAgentMasterName, OperationContextHolder.getMasterNick());
            ext.put(CommonConstants.uploadByAgentRole, OperationContextHolder.getRoleType());
        }

        if (StringUtils.isNotBlank(request.getBizTag())) {
            ext.put(BizConstants.BIZ_TAG, request.getBizTag());
        }

        if (StringUtils.isNotBlank(request.getUsageType())) {
            ext.put(CommonConstants.KEY_USAGE_TYPE, request.getUsageType());
        }

        if (StringUtils.isNotBlank(request.getUsageMemo())) {
            ext.put(CommonConstants.KEY_USAGE_MEMO, request.getUsageMemo());
        }

        MaterialModelQuery existQuery = new MaterialModelQuery();
        existQuery.setMaterialType(MaterialType.cloth.name());
        existQuery.setUserId(OperationContextHolder.getMasterUserId());

        Long existedClothNum = materialModelService.queryMaterialModelCount(existQuery);
        ext.put(CommonConstants.KEY_CLOTH_NUM, String.valueOf(existedClothNum != null ? (existedClothNum + 1) : 1));
    }

    @Override
    protected Integer fetchOwnUser(AddClothMaterialRequest request) {
        Integer userId = OperationContextHolder.getMasterUserId();
        if (OperationContextHolder.getRoleType() == RoleTypeEnum.DISTRIBUTOR) {
            Integer principalId = request.getPrincipalId();
            AssertUtil.assertNotNull(principalId, ResultCode.PARAM_INVALID, "渠道商代传素材，principalId不能为空");
            log.info("渠道商代传素材，设置当前服装的主体用户id={}", principalId);
            userId = principalId;
        }
        return userId;
    }

    /**
     * 构建激活词
     *
     * @param materialType 素材类型
     * @param labelType    打标类型
     * @param noCutout     是否不抠图
     * @return 激活词
     */
    private String buildActivateKey(String materialType, String labelType, boolean noCutout) {
        LabelTypeEnum labelTypeEnum = LabelTypeEnum.getByCode(labelType);
        if (labelTypeEnum == LabelTypeEnum.MINI) {
            return systemConfigService.queryValueByKey(LORA_MINI_ACTIVATE_KEYS);
        }
        boolean isNew = systemConfigService.queryBoolValue(NO_CUTOUT_MERCHANT_LIST, false) || noCutout;
        return LoraActivateKeys.getActivateKey(materialType, isNew);
    }

    /**
     * 解析打标类型
     *
     * @param userId    用户id
     * @param labelType 打标类型
     * @return 打标类型
     */
    private LabelTypeEnum parseLabelType(Integer userId, String labelType) {
        LabelTypeEnum labelTypeEnum = LabelTypeEnum.getByCode(labelType);

        if (OperationContextHolder.isBackRole() && labelTypeEnum != null) {
            log.info("后台角色，强制按照用户选择的进行");
            return labelTypeEnum;
        }

        if (systemConfigService.isInJsonArray(LORA_MINI_LABEL_MERCHANT_LIST, userId)) {
            return LabelTypeEnum.MINI;
        }

        if (systemConfigService.isInJsonArray(LORA_NEW_LABEL_MERCHANT_LIST, userId)) {
            return LabelTypeEnum.DETAILS;
        }

        JSONObject switchJson = systemConfigService.queryJsonValue(LORA_LABEL_SWITCH);
        float flag = switchJson.getFloatValue(LabelTypeEnum.MINI.getCode());
        if (GrayscaleTestUtils.isHit(flag, "服装训练-极简打标灰度")) {
            return LabelTypeEnum.MINI;
        }

        flag = switchJson.getFloatValue(LabelTypeEnum.DETAILS.getCode());
        if (GrayscaleTestUtils.isHit(flag, "服装训练-精准打标灰度")) {
            return LabelTypeEnum.DETAILS;
        }

        return LabelTypeEnum.DEFAULT;
    }

    /**
     * 构建服装打标prompt
     *
     * @param labelType 打标类型
     * @return 打标prompt
     */
    private String buildClothLabelPrompt(LabelTypeEnum labelType) {
        if (labelType == LabelTypeEnum.STRUCTURAL) {
            return "";
        }
        String prompt = systemConfigService.queryValueByKey(labelType.getPromptKey());

        // 转义换行符，将字符串的换行改为换行符\n
        prompt = CommonUtil.unescapeLineBreak(prompt);
        prompt = ComfyUIUtils.parseParams(prompt);

        return prompt;
    }
}
