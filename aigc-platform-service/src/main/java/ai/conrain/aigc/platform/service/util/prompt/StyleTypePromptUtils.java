package ai.conrain.aigc.platform.service.util.prompt;

import ai.conrain.aigc.platform.service.model.common.KeyValue;

import java.util.List;
import java.util.Map;

public class StyleTypePromptUtils {


    /**
     * 构建风格分类提示词
     * @param styleMap 风格分类Map，Key为一级分类名称，Value为二级分类对象列表
     * @return 完整的提示词字符串
     */
    public String buildPrompt(Map<String, List<KeyValue>> styleMap) {
        if (styleMap == null || styleMap.isEmpty()) {
            return "";
        }

        StringBuilder promptBuilder = new StringBuilder();
        
        // 固定开头
        promptBuilder.append("请帮我按照以下分类将图片识别到具体的二级风格，说明以下格式为一级分类和二级分类的说明\n\n");
        
        // 遍历渲染中间部分
        int categoryIndex = 1;
        for (Map.Entry<String, List<KeyValue>> entry : styleMap.entrySet()) {
            String primaryCategory = entry.getKey();
            List<KeyValue> subStyles = entry.getValue();
            
            if (subStyles == null || subStyles.isEmpty()) {
                continue;
            }
            
            // 一级分类标题
            promptBuilder.append(categoryIndex).append(". ClothStyleFirstCategory: ").append(primaryCategory).append("\n");
            
            // 二级分类列表
            promptBuilder.append("   二级分类: ");
            for (int i = 0; i < subStyles.size(); i++) {
                if (i > 0) {
                    promptBuilder.append(", ");
                }
                promptBuilder.append(subStyles.get(i).getKey());
            }
            promptBuilder.append("\n");
            
            // 二级分类详细描述
            for (KeyValue keyValue : subStyles) {
                promptBuilder.append(" * ").append(keyValue.getKey()).append(": ")
                           .append(keyValue.getValue()).append("\n");
            }
            
            promptBuilder.append("\n");
            categoryIndex++;
        }
        
        // 固定结尾
        promptBuilder.append("以上是所有的风格分类，请帮我匹配最合适的二级分类，输出格式 ClothStyleFirstCategory|二级分类\n")
                    .append("output样例如：简约利落|cleanfit");
        
        return promptBuilder.toString();
    }

    /**
     * 重载方法，兼容原有调用
     */
    public String buildPrompt() {
        return buildPrompt(null);
    }
}
