/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.deerapi;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * deerAPI的模型枚举
 *
 * <AUTHOR>
 * @version : DeerModelEnum.java, v 0.1 2025/8/6 19:46 renxiao.wu Exp $
 */
@Getter
public enum DeerModelEnum {
    GPT_4_1("gpt-4.1", false),

    GPT_5_NANO("gpt-5-nano", false),

    GPT_5_MINI("gpt-5-mini", false),

    GPT_5("gpt-5", false),

    GEMINI_2_5_PRO("gemini-2.5-pro", false),

    GEMINI_2_5_FLASH("gemini-2.5-flash", false),

    GEMINI_2_5_FLASH_IMAGE_PREVIEW("gemini-2.5-flash-image-preview", true),
    ;

    /** 枚举码 */
    private final String code;

    /** 是否生成内容 */
    private final boolean isGenerateContent;

    private DeerModelEnum(String code, boolean isGenerateContent) {
        this.code = code;
        this.isGenerateContent = isGenerateContent;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static DeerModelEnum getByCode(String code, DeerModelEnum defaultEnum) {
        if (StringUtils.isBlank(code)) {
            return defaultEnum;
        }

        for (DeerModelEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return defaultEnum;
    }
}
