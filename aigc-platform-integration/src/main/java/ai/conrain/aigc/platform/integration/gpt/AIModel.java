/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.gpt;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version : AIModel.java, v 0.1 2025/8/6 20:04 renxiao.wu Exp $
 */
public class AIModel {

    public enum Status {
        OK,
        ERROR
    }

    @Data
    public static class GptResponse {
        private String text;
        private Status status;
        private List<Base64ImageInfo> imageBase64List;

        public GptResponse() {
        }

        public GptResponse(String text, Status status) {
            this.text = text;
            this.status = status;
        }

        public GptResponse(String text, Status status, List<Base64ImageInfo> imageBase64List) {
            this.text = text;
            this.status = status;
            this.imageBase64List = imageBase64List;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Base64ImageInfo {
        /** 图片类型 */
        @JSONField(name = "mimeType")
        private String type;
        /** base64内容 */
        private String data;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApiConfig {
        private String name;
        private String apiKey;
        private String endpoint;
    }

    public static JSONObject createTextContent(String text) {
        JSONObject content = new JSONObject();
        content.put("type", "text");
        content.put("text", text);
        return content;
    }

    public static JSONObject createImageContent(String imgUrl) {
        JSONObject imageUrlJson = new JSONObject();
        imageUrlJson.put("url", imgUrl);

        JSONObject content = new JSONObject();
        content.put("type", "image_url");
        content.put("image_url", imageUrlJson);
        return content;
    }

    public static JSONObject createTextContentForGemini(String text) {
        JSONObject content = new JSONObject();
        content.put("text", text);
        return content;
    }

    public static JSONObject createImageContentForGemini(String imgUrl) {
        if (StringUtils.startsWith(imgUrl, "https://") || StringUtils.startsWith(imgUrl, "http://")) {
            throw new RuntimeException("imgUrl must be local path");
        }

        if (StringUtils.startsWith(imgUrl, "data:image/jpeg;base64,")) {
            imgUrl = StringUtils.substringAfter(imgUrl, "data:image/jpeg;base64,");
        }

        JSONObject imageUrlJson = new JSONObject();
        imageUrlJson.put("mime_type", "image/jpeg");
        imageUrlJson.put("data", imgUrl);

        JSONObject content = new JSONObject();
        content.put("inline_data", imageUrlJson);

        return content;
    }

    public static final JSONObject GenerationConfig = new JSONObject();
    static {
        JSONArray array = new JSONArray();
        array.add("TEXT");
        array.add("IMAGE");
        GenerationConfig.put("responseModalities", array);
    }
}
