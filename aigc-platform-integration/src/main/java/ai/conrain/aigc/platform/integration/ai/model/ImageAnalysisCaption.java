package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * 图像描述模型类
 * 用于接收和解析JSON格式的图像描述数据
 * 示例：https://conrain.yuque.com/org-wiki-conrain-pcgdb4/mcwagh/sfdt5b5h264wsa9x#wTDpi
 */
@Data
public class ImageAnalysisCaption {

    /**
     * 模特信息
     */
    @JSONField(name = "Model")
    private Model model;

    /**
     * 服装信息
     */
    @JSONField(name = "Clothing")
    private Clothing clothing;

    /**
     * 镜头语言
     */
    @JSONField(name = "Lens_Language")
    private LensLanguage lensLanguage;

    /**
     * 拍摄主题
     */
    @JSONField(name = "Shooting_Theme")
    private ShootingTheme shootingTheme;

    /**
     * 模特信息类
     */
    @Data
    public static class Model {

        /**
         * 年龄
         */
        @JSONField(name = "Age")
        private String age;

        /**
         * 种族
         */
        @J<PERSON>NField(name = "Race")
        private String race;

        /**
         * 肤色信息
         */
        @JSONField(name = "Skin")
        private Skin skin;

        /**
         * 性别
         */
        @JSONField(name = "Gender")
        private String gender;

        /**
         * 妆容
         */
        @JSONField(name = "Makeup")
        private String makeup;

        /**
         * 姿势
         */
        @JSONField(name = "Posture")
        private String posture;

        /**
         * 发型
         */
        @JSONField(name = "Hairstyle")
        private String hairstyle;

        /**
         * 头部位置
         */
        @JSONField(name = "Head_Position")
        private String headPosition;

        /**
         * 面部表情
         */
        @JSONField(name = "Facial_Expression")
        private String facialExpression;
    }

    /**
     * 肤色信息类
     */
    @Data
    public static class Skin {

        /**
         * 肤色
         */
        @JSONField(name = "Skin_Tone")
        private String skinTone;

        /**
         * 肤色特征
         */
        @JSONField(name = "Skin_Feature")
        private String skinFeature;

    }

    /**
     * 服装信息类
     */
    @Data
    public static class Clothing {

        /**
         * 上装
         */
        @JSONField(name = "Top")
        private Top top;

        /**
         * 鞋子
         */
        @JSONField(name = "Shoes")
        private String shoes;

        /**
         * 下装
         */
        @JSONField(name = "Bottom")
        private Bottom bottom;

        /**
         * 配饰
         */
        @JSONField(name = "Accessories")
        private String accessories;

        /**
         * 内搭
         */
        @JSONField(name = "Layering_Piece")
        private String layeringPiece;
    }

    /**
     * 上装类
     */
    @Data
    public static class Top {

        /**
         * 合身度
         */
        @JSONField(name = "Fit")
        private String fit;

        /**
         * 类型
         */
        @JSONField(name = "Type")
        private String type;

        /**
         * 颜色
         */
        @JSONField(name = "Color")
        private String color;

        /**
         * 样式
         */
        @JSONField(name = "Style")
        private String style;

        /**
         * 长度
         */
        @JSONField(name = "Length")
        private String length;

        /**
         * 袖长
         */
        @JSONField(name = "Sleeve_Length")
        private String sleeveLength;

        /**
         * 图案和特征
         */
        @JSONField(name = "Pattern_and_Feature")
        private String patternAndFeature;
    }

    /**
     * 下装类
     */
    @Data
    public static class Bottom {

        /**
         * 类型
         */
        @JSONField(name = "Type")
        private String type;

        /**
         * 颜色
         */
        @JSONField(name = "Color")
        private String color;

        /**
         * 样式
         */
        @JSONField(name = "Style")
        private String style;

        /**
         * 长度
         */
        @JSONField(name = "Length")
        private String length;

        /**
         * 图案和特征
         */
        @JSONField(name = "Pattern_and_Feature")
        private String patternAndFeature;

        /**
         * 腰线
         */
        @JSONField(name = "Waistline")
        private String waistline;
    }

    /**
     * 镜头语言类
     */
    @Data
    public static class LensLanguage {

        /**
         * 边框
         */
        @JSONField(name = "Frame")
        private String frame;

        /**
         * 视角
         */
        @JSONField(name = "Viewpoint")
        private String viewpoint;

        /**
         * 模糊程度
         */
        @JSONField(name = "Blur_Degree")
        private String blurDegree;

        /**
         * 构图
         */
        @JSONField(name = "Composition")
        private String composition;

        /**
         * 角度高度
         */
        @JSONField(name = "Angle_Height")
        private String angleHeight;

        /**
         * 头部完整性
         */
        @JSONField(name = "Head_Completeness")
        private String headCompleteness;

        /**
         * 拼贴类型
         */
        @JSONField(name = "Collage_Type")
        private String collageType;
    }

    /**
     * 拍摄主题类
     */
    @Data
    public static class ShootingTheme {

        /**
         * 灯光
         */
        @JSONField(name = "Lighting")
        private Lighting lighting;

        /**
         * 流派
         */
        @JSONField(name = "Intended_Use")
        private String genre;

        /**
         * 拍摄场景
         */
        @JSONField(name = "Shooting_Scene")
        private String shootingScene;

        /**
         * 叠加元素
         */
        @JSONField(name = "Overlay_Elements")
        private String overlayElements;

        /**
         * 后期处理风格
         */
        @JSONField(name = "Post_Processing_Style")
        private String postProcessingStyle;
    }

    /**
     * 灯光类
     */
    @Data
    public static class Lighting {

        /**
         * 氛围
         */
        @JSONField(name = "Atmosphere")
        private String atmosphere;

        /**
         * 色温
         */
        @JSONField(name = "Color_Temperature")
        private String colorTemperature;
    }
}