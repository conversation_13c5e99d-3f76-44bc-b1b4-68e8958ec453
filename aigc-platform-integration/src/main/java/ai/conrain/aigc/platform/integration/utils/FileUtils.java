package ai.conrain.aigc.platform.integration.utils;

import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class FileUtils {
    public static List<FileVO> sortFileVOs(List<FileVO> fileVOs) {

        if (fileVOs == null) {
            throw new NullPointerException("文件列表为null");
        }

        // 复制原始列表，避免修改
        List<FileVO> sortedList = new ArrayList<>(fileVOs);

        // 使用自定义比较器排序
        sortedList.sort((fileVO1, fileVO2) -> {
            Integer number1 = extractLeadingNumber(fileVO1.getFileName());
            Integer number2 = extractLeadingNumber(fileVO2.getFileName());
            if (number1 != null && number2 != null) {
                return number1.compareTo(number2);
            } else if (number1 != null) {
                return -1; // number1 有值，排在前面
            } else if (number2 != null) {
                return 1; // number2 有值，排在前面
            } else {
                return fileVO1.getFileName().compareTo(fileVO2.getFileName()); // 按字典顺序排序
            }
        });

        return sortedList; // 返回排序后的新列表
    }

    private static Integer extractLeadingNumber(String fileName) {
        Pattern pattern = Pattern.compile("^(\\d+)_");
        Matcher matcher = pattern.matcher(fileName);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        return null; // 没有找到数字前缀，返回 null
    }
}
