package ai.conrain.aigc.platform.dal.pgsql.entity;

import ai.conrain.aigc.platform.dal.example.ImageExample;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 图片批量统计参数
 * 用于动态构建查询字段的批量统计
 */
@Data
public class ImageBatchCountDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 查询条件
     */
    private ImageExample example;

    /**
     * 需要统计的字段配置列表
     */
    private List<MetadataFieldDO> fieldConfigs;
}
