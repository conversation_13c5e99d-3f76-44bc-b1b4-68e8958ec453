package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.ImageRecordExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageRecordDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ImageRecordDAO {
    List<ImageRecordDO> findPageable(ImageRecordExample query);
    long countPageable(ImageRecordExample query);
    List<ImageRecordDO> findImageGroupPageable(ImageRecordExample query);
    long countImageGroupPageable(ImageRecordExample query);
}
