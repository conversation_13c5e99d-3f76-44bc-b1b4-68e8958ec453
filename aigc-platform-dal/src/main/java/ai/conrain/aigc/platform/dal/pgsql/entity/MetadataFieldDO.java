package ai.conrain.aigc.platform.dal.pgsql.entity;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 元数据字段配置
 */
@Data
public class MetadataFieldDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 字段名称（metadata中的key）
     */
    private String fieldName;

    /**
     * 需要统计的值列表
     */
    private List<String> values;

    public MetadataFieldDO() {
    }

    public MetadataFieldDO(String fieldName, List<String> values) {
        this.fieldName = fieldName;
        this.values = values;
    }
}