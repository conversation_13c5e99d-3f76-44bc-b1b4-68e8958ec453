package ai.conrain.aigc.platform.dal.pgsql.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 图像标注表用户标注数据，存储用户和大模型打标的数据
 * 对应数据表：image_caption_user
 */
@Data
public class ImageCaptionUserDO implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 图片ID
     */
    private Integer imageId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 对原始的image_caption_user记录进行修改
     */
    private Integer originalId;

    /**
     * 标注类型
     */
    private String type;

    /**
     * 标注
     */
    private String caption;

    /**
     * 标注版本
     */
    private String captionVersion;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 是否删除
     */
    private Boolean deleted;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}