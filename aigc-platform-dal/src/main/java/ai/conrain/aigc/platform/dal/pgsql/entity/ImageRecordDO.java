package ai.conrain.aigc.platform.dal.pgsql.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class ImageRecordDO implements Serializable {
    /**
     * 图像ID，自增主键
     */
    private Integer id;

    /** 场景的图片 id */
    private Integer sceneId;

    /**
     * 打标类型
     */
    private String type;

    /**
     * 图像原始URL地址
     */
    private String url;

    private String pairUrl;

    /**
     * 展示图像URL地址
     */
    private String showImgUrl;

    /**
     * 图片路径
     */
    private String imagePath;

    /**
     * 图片内容哈希
     */
    private String imageHash;

    /**
     * 图像元数据，JSON格式
     */
    private String metadata;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 图片组标题结果，来自 image_group_caption.result
     */
    private String result;

    /**
     * 聚合用户编辑信息，JSON格式：[{user_id, editTime}]
     */
    private String agg;

    /**
     * 服装类型
     */
    private String clothTypeDesc;

    /**
     * 流派
     */
    private String intendedUse;
}
