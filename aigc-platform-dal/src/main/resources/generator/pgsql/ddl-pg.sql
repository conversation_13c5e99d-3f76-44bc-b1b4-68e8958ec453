-- 启用vector插件
CREATE EXTENSION vector;

-- 20250723 agent项目
DROP TABLE IF EXISTS image;
CREATE TABLE image (
    id SERIAL PRIMARY KEY,
    type VARCHAR(32),
    url VARCHAR(1024) NOT NULL,
    show_img_url VARCHAR(1024),
    image_path VARCHAR,
    image_hash VARCHAR,
    metadata JSONB,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 表注释
COMMENT ON TABLE image IS '图像基本信息表';

-- 字段注释
COMMENT ON COLUMN image.id IS '图像ID，自增主键';
COMMENT ON COLUMN image.url IS '图像原始URL地址';
COMMENT ON COLUMN image.show_img_url IS '展示图像URL地址';
COMMENT ON COLUMN image.type IS '图像类型，用于区分服装图、风格示意图';
COMMENT ON COLUMN image.image_path IS '图片路径';
COMMENT ON COLUMN image.image_hash IS '图片内容哈希';
COMMENT ON COLUMN image.metadata IS '图像元数据，JSON格式';
COMMENT ON COLUMN image.ext_info IS '扩展信息，JSON格式';
COMMENT ON COLUMN image.create_time IS '创建时间';
COMMENT ON COLUMN image.modify_time IS '修改时间';
COMMENT ON COLUMN image.deleted IS '软删除标记';

-- 在type字段上创建索引，加速按类型查询
CREATE INDEX idx_image_type ON image(type) WHERE deleted = FALSE;

-- 在create_time上创建索引，加速按时间范围查询
CREATE INDEX idx_image_create_time ON image(create_time);

CREATE INDEX idx_image_deleted_type_id ON image(deleted, type, id);

-- 创建图像标注表
DROP TABLE IF EXISTS image_caption;
CREATE TABLE image_caption (
    id SERIAL PRIMARY KEY,
    image_id INTEGER NOT NULL,
    image_type VARCHAR,
    caption JSONB,
    caption_version VARCHAR,

    pre_caption JSONB,

    cloth_gender_type VARCHAR,
    age_group VARCHAR,
    quality_score FLOAT,
    genre VARCHAR,

    img_emb VECTOR(1024),
    bg_img_emb VECTOR(1024),
    model_facial_img_emb VECTOR(1024),
    model_pose_img_emb VECTOR(1024),
    sort_bg_text_emb VECTOR(1024),
    sort_facial_expression_text_emb VECTOR(1024),
    sort_accessories_text_emb VECTOR(1024),
    sort_pose_text_emb VECTOR(1024),

    cloth_text_emb VECTOR(1024),

    cloth_style_text_emb VECTOR(256),
    bg_text_emb VECTOR(256),
    accessories_text_emb VECTOR(256),
    hairstyle_text_emb VECTOR(256),
    pose_text_emb VECTOR(256),

    ext_info VARCHAR,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 表注释
COMMENT ON TABLE image_caption IS '图像标注表，存储图像的多模态特征向量及标注文本，用于内容检索和分类';

-- 字段注释
COMMENT ON COLUMN image_caption.id IS '自增主键ID';
COMMENT ON COLUMN image_caption.image_id IS '关联的图像ID，用于跨表查询';
COMMENT ON COLUMN image_caption.image_type IS '图像类型，如服装图、风格示意图';
COMMENT ON COLUMN image_caption.caption_text IS '图像标注的文本描述';
COMMENT ON COLUMN image_caption.caption IS '图像标注的完整文本描述，JSON格式存储';
COMMENT ON COLUMN image_caption.caption_version IS '标注版本号，用于区分不同标注模型或规则';
COMMENT ON COLUMN image_caption.pre_caption IS '预标注的文本描述，JSON格式存储';
COMMENT ON COLUMN image_caption.cloth_gender_type IS '服装性别类型，如男装、女装、通用';
COMMENT ON COLUMN image_caption.age_group IS '年龄段，如青年、中年、老年';
COMMENT ON COLUMN image_caption.genre IS '图像的流派';
COMMENT ON COLUMN image_caption.quality_score IS '质量分，0-1分，用于评估图像质量';
COMMENT ON COLUMN image_caption.img_emb IS '整图特征向量（1024维）';
COMMENT ON COLUMN image_caption.bg_img_emb IS '背景抠图特征向量（1024维）';
COMMENT ON COLUMN image_caption.model_facial_img_emb IS '模特面部抠图特征向量（1024维）';
COMMENT ON COLUMN image_caption.model_pose_img_emb IS '模特姿势抠图特征向量（1024维）';
COMMENT ON COLUMN image_caption.cloth_style_text_emb IS '服装款式描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.cloth_text_emb IS '服装整体描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.bg_text_emb IS '背景道具描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.accessories_text_emb IS '配饰描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.hairstyle_text_emb IS '发型描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.pose_text_emb IS '姿势描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.sort_bg_text_emb IS '背景分类文本向量（1024维，用于聚类或检索）';
COMMENT ON COLUMN image_caption.sort_facial_expression_text_emb IS '表情分类文本向量（1024维，用于聚类或检索）';
COMMENT ON COLUMN image_caption.sort_accessories_text_emb IS '配饰分类文本向量（1024维，用于聚类或检索）';
COMMENT ON COLUMN image_caption.sort_pose_text_emb IS '姿势分类文本向量（1024维，用于聚类或检索）';
COMMENT ON COLUMN image_caption.ext_info IS '扩展信息，JSON格式存储非结构化附加数据';
COMMENT ON COLUMN image_caption.create_time IS '记录创建时间，默认为当前时间戳';
COMMENT ON COLUMN image_caption.modify_time IS '记录最后修改时间，默认为当前时间戳';
COMMENT ON COLUMN image_caption.deleted IS '软删除标记，FALSE表示未删除，TRUE表示已删除';
---------------------------------- 标注表 ----------------------------------

DROP INDEX IF EXISTS idx_image_caption_image_id;
CREATE INDEX idx_image_caption_image_id ON image_caption(image_id);

DROP INDEX IF EXISTS idx_caption_version;
CREATE INDEX idx_caption_version ON image_caption(caption_version);

DROP INDEX IF EXISTS idx_create_time_filtered;
CREATE INDEX idx_create_time_filtered ON image_caption(create_time);

DROP INDEX IF EXISTS idx_image_caption_image_type;
CREATE INDEX idx_image_caption_image_type ON image_caption(image_type);

DROP INDEX IF EXISTS idx_image_caption_cloth_gender_type;
CREATE INDEX idx_image_caption_cloth_gender_type ON image_caption(cloth_gender_type);

DROP INDEX IF EXISTS idx_image_caption_age_group;
CREATE INDEX idx_image_caption_age_group ON image_caption(age_group);

DROP INDEX IF EXISTS idx_image_caption_genre;
CREATE INDEX idx_image_caption_genre ON image_caption(genre);

DROP INDEX IF EXISTS idx_image_caption_pre_caption;
CREATE INDEX idx_image_caption_pre_caption ON image_caption(pre_caption);

DROP INDEX IF EXISTS idx_image_caption_quality_score;
CREATE INDEX idx_image_caption_quality_score ON image_caption(quality_score);

-- 为过滤条件创建B-tree索引
DROP INDEX IF EXISTS idx_filter_conditions;
CREATE INDEX idx_filter_conditions ON image_caption(image_type, cloth_gender_type, quality_score, age_group, genre, deleted)
WHERE cloth_style_text_emb IS NOT NULL;

DROP INDEX IF EXISTS idx_hnsw_emb_cloth_style;
CREATE INDEX idx_hnsw_emb_cloth_style ON image_caption
USING hnsw ((cloth_style_text_emb::vector(256)) vector_cosine_ops)
WITH (m=32, ef_construction=200);

alter table image_caption add column quality_score float ;

alter table image_caption add column genre VARCHAR;

UPDATE image_caption
SET age_group =
    CASE
        WHEN caption->'Model'->>'Age' = '0-3' THEN 'Young'
        WHEN caption->'Model'->>'Age' IN ('3-5', '5-9') THEN 'Child'
        WHEN caption->'Model'->>'Age' IN ('10-14') THEN 'Teenager'
        WHEN caption->'Model'->>'Age' IN ('15-25','26-45', '46-60') THEN 'Adult'
        WHEN caption->'Model'->>'Age' = '60+' THEN 'Elderly'
        ELSE 'Unknown'
    END,
    modify_time = CURRENT_TIMESTAMP
WHERE deleted = false;

alter table image_caption add column pre_caption JSONB;
COMMENT ON COLUMN image_caption.pre_caption IS '预标注的文本描述，JSON格式存储';


CREATE INDEX idx_image_caption_viewpoint ON image_caption USING BTREE ((caption->'Lens_Language'->>'Viewpoint'));
CREATE INDEX idx_image_caption_composition ON image_caption USING BTREE ((caption->'Lens_Language'->>'Composition'));
CREATE INDEX idx_image_caption_top_cate_1 ON image_caption USING BTREE ((pre_caption->>'top_cate_1'));
CREATE INDEX idx_image_caption_top_cate_2 ON image_caption USING BTREE ((pre_caption->>'top_cate_2'));
CREATE INDEX idx_image_caption_suit_cate_1 ON image_caption USING BTREE ((pre_caption->>'suit_cate_1'));
CREATE INDEX idx_image_caption_suit_cate_2 ON image_caption USING BTREE ((pre_caption->>'suit_cate_2'));
CREATE INDEX idx_image_caption_usage_cate_1 ON image_caption USING BTREE ((pre_caption->>'usage_cate_1'));
CREATE INDEX idx_image_caption_usage_cate_2 ON image_caption USING BTREE ((pre_caption->>'usage_cate_2'));
CREATE INDEX idx_image_caption_bottom_cate_1 ON image_caption USING BTREE ((pre_caption->>'bottom_cate_1'));
CREATE INDEX idx_image_caption_bottom_cate_2 ON image_caption USING BTREE ((pre_caption->>'bottom_cate_2'));
CREATE INDEX idx_image_caption_onepiece_cate_1 ON image_caption USING BTREE ((pre_caption->>'onepiece_cate_1'));
CREATE INDEX idx_image_caption_onepiece_cate_2 ON image_caption USING BTREE ((pre_caption->>'onepiece_cate_2'));

CREATE INDEX idx_ic_deleted_type_image_id ON image_caption(deleted, image_type, image_id);
---------------------------------- 标注表 ----------------------------------

CREATE TABLE IF NOT EXISTS image_caption_user (
  id SERIAL PRIMARY KEY,
  image_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  original_id INTEGER,
  caption JSONB,
  caption_version VARCHAR,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted BOOLEAN NOT NULL DEFAULT FALSE
);
COMMENT ON TABLE image_caption_user IS '图像标注表用户标注数据，存储用户和大模型打标的数据';
COMMENT ON COLUMN image_caption_user.id IS '主键';
COMMENT ON COLUMN image_caption_user.image_id IS '图片ID';
COMMENT ON COLUMN image_caption_user.user_id IS '用户ID';
COMMENT ON COLUMN image_caption_user.original_id IS '对原始的image_caption_user记录进行修改';
COMMENT ON COLUMN image_caption_user.caption IS '标注';
COMMENT ON COLUMN image_caption_user.caption_version IS '标注版本';
COMMENT ON COLUMN image_caption_user.create_time IS '创建时间';
COMMENT ON COLUMN image_caption_user.modify_time IS '修改时间';
COMMENT ON COLUMN image_caption_user.deleted IS '是否删除';

-- 复合索引
CREATE INDEX idx_image_caption_user_image_id_user_id ON image_caption_user(image_id, user_id) WHERE deleted = FALSE;

CREATE INDEX idx_icu_caption_gin ON image_caption_user USING GIN (caption);

CREATE TABLE IF NOT EXISTS image_group (
  id SERIAL PRIMARY KEY,
  image_ids JSONB,
  metadata JSONB,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted BOOLEAN NOT NULL DEFAULT FALSE
);
COMMENT ON TABLE image_group IS '图像组，由多种图组成的pair对';
COMMENT ON COLUMN image_group.id IS '主键';
COMMENT ON COLUMN image_group.image_ids IS '图片ID列表';
COMMENT ON COLUMN image_group.metadata IS '图片组的元数据';
COMMENT ON COLUMN image_group.create_time IS '创建时间';
COMMENT ON COLUMN image_group.modify_time IS '修改时间';
COMMENT ON COLUMN image_group.deleted IS '是否删除';

CREATE TABLE IF NOT EXISTS image_group_caption (
  id SERIAL PRIMARY KEY,
  image_group_id INTEGER NOT NULL,
  image_caption_ids JSONB,
  result JSONB NOT NULL DEFAULT '[]'::jsonb,
  caption JSONB,
  caption_log JSONB,
  caption_version VARCHAR,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted BOOLEAN NOT NULL DEFAULT FALSE
);
COMMENT ON TABLE image_group_caption IS '图像组标注表，pair对打标的最终数据';
COMMENT ON COLUMN image_group_caption.id IS '主键';
COMMENT ON COLUMN image_group_caption.image_group_id IS '图片组ID';
COMMENT ON COLUMN image_group_caption.image_caption_ids IS '图片标注ID列表';
COMMENT ON COLUMN image_group_caption.result IS '多人打标结果（意见一致，分歧）';
COMMENT ON COLUMN image_group_caption.caption IS '标注';
COMMENT ON COLUMN image_group_caption.caption_log IS '标注日志';
COMMENT ON COLUMN image_group_caption.caption_version IS '标注版本';
COMMENT ON COLUMN image_group_caption.create_time IS '创建时间';
COMMENT ON COLUMN image_group_caption.modify_time IS '修改时间';
COMMENT ON COLUMN image_group_caption.deleted IS '是否删除';

CREATE INDEX idx_image_group_caption_image_group_id ON image_group_caption(image_group_id) WHERE deleted = FALSE;

CREATE TABLE IF NOT EXISTS image_group_caption_user (
  id SERIAL PRIMARY KEY,
  image_group_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  caption JSONB,
  caption_version VARCHAR,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted BOOLEAN NOT NULL DEFAULT FALSE
);
COMMENT ON TABLE image_group_caption_user IS '图像组标注表用户标注数据，多人打标';
COMMENT ON COLUMN image_group_caption_user.id IS '主键';
COMMENT ON COLUMN image_group_caption_user.image_group_id IS '图片组ID';
COMMENT ON COLUMN image_group_caption_user.user_id IS '用户ID';
COMMENT ON COLUMN image_group_caption_user.caption IS '标注';
COMMENT ON COLUMN image_group_caption_user.caption_version IS '标注版本';
COMMENT ON COLUMN image_group_caption_user.create_time IS '创建时间';
COMMENT ON COLUMN image_group_caption_user.modify_time IS '修改时间';
COMMENT ON COLUMN image_group_caption_user.deleted IS '是否删除';

CREATE INDEX idx_image_group_caption_user_image_group_id_user_id ON image_group_caption_user(image_group_id, user_id) WHERE deleted = FALSE;

CREATE TABLE IF NOT EXISTS "user" (
  "id" SERIAL PRIMARY KEY,
  "username" VARCHAR NOT NULL UNIQUE,
  "password_hash" VARCHAR NOT NULL,
  "nickname" VARCHAR NOT NULL,
  "role" VARCHAR NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE "user" IS '用户基础信息表';
COMMENT ON COLUMN "user"."id" IS '用户唯一标识符（主键）';
COMMENT ON COLUMN "user"."username" IS '用户登录名（唯一）';
COMMENT ON COLUMN "user"."password_hash" IS '用户密码的哈希值（加密存储）';
COMMENT ON COLUMN "user"."nickname" IS '用户显示昵称';
COMMENT ON COLUMN "user"."role" IS '用户角色标识（如：admin/llm/annotator）';
COMMENT ON COLUMN "user"."create_time" IS '记录创建时间（自动生成）';
COMMENT ON COLUMN "user"."modify_time" IS '记录最后更新时间（自动更新）';

CREATE TABLE "tagging_attribute" (
    "id" int4 NOT NULL,
    "key" varchar NOT NULL,
    "name" varchar NOT NULL,
    "display_name" varchar NOT NULL,
    "enumerations" jsonb,
    "display_enumerations" jsonb,
    "is_active" bool NOT NULL DEFAULT true,
    "sort_order" int4 NOT NULL DEFAULT 0,
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "modify_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "type" varchar NOT NULL,
    "format" varchar NOT NULL DEFAULT 'string'::character varying,
    "disabled" bool NOT NULL DEFAULT false,
    PRIMARY KEY ("id")
);

-- Indices
CREATE UNIQUE INDEX tagging_attribute_key_type_key ON tagging_attribute USING btree (key, type);

-- 表注释：描述 tagging_attribute 表的用途
COMMENT ON TABLE tagging_attribute IS '属性定义表，用于存储图像描述（caption）中可配置的属性元信息';

-- 字段注释
COMMENT ON COLUMN tagging_attribute.id IS '主键ID，唯一标识一个属性';
COMMENT ON COLUMN tagging_attribute.key IS '属性键值（英文标识），用于程序中引用，如 color, style, material';
COMMENT ON COLUMN tagging_attribute.name IS '属性名称（中文标识），用于前端展示或内部识别';
COMMENT ON COLUMN tagging_attribute.display_name IS '前端展示名称，如“颜色”、“风格”';
COMMENT ON COLUMN tagging_attribute.enumerations IS '枚举值列表（JSONB格式），用于下拉选择，格式: [{"value": "red", "label": "红色"}]';
COMMENT ON COLUMN tagging_attribute.display_enumerations IS '国际化枚举展示名（JSONB），如 {"en": "Red", "zh": "红色"}，可选';
COMMENT ON COLUMN tagging_attribute.is_active IS '是否启用：true 表示该属性可用，false 表示停用（软删除）';
COMMENT ON COLUMN tagging_attribute.sort_order IS '排序权重，数值越小越靠前';
COMMENT ON COLUMN tagging_attribute.create_time IS '记录创建时间，自动填充';
COMMENT ON COLUMN tagging_attribute.modify_time IS '最后修改时间，更新时自动刷新';
COMMENT ON COLUMN tagging_attribute.type IS '属性类型，用于区分应用场景，如 clothing（服装）、background（背景）、accessory（配饰）等';
COMMENT ON COLUMN tagging_attribute.format IS '值格式类型，默认 string；可选：string, number, boolean, date, enum';
COMMENT ON COLUMN tagging_attribute.disabled IS '是否禁用：true 表示禁用（不显示），与 is_active 配合使用，用于灰度控制';

-- 搜索任务记录
DROP TABLE IF EXISTS search_task;
CREATE TABLE "search_task" (
  "id" SERIAL PRIMARY KEY,
  "user_id" INTEGER,
  "user_session_id" INTEGER,
  "user_session_task_id" INTEGER,
  "cloth_img_detail" VARCHAR,
  "cloth_analysis" VARCHAR,
  "ref_img_detail" VARCHAR,
  "ref_img_analysis" VARCHAR,
  "search_options" VARCHAR,
  "status" VARCHAR,
  "ret_summary" VARCHAR,
  "ext_info" VARCHAR,
  "create_time" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "modify_time" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

comment on table "search_task" is '搜索记录历史';
comment on column "search_task"."id" is '主键';
comment on column "search_task"."user_id" is 'MuseGate用户id';
comment on column "search_task"."cloth_img_detail" is '用户上传的衣服图片信息,json格式,url/clothGender/clothType等';
comment on column "search_task"."cloth_analysis" is '衣服图片分析结果,json格式,打标结果';
comment on column "search_task"."ref_img_detail" is '用户上传的参考图片信息,json array格式,url/refScopes等';
comment on column "search_task"."ref_img_analysis" is '参考图片分析结果,json array格式,打标结果';
comment on column "search_task"."search_options" is '搜索参数,json格式';
comment on column "search_task"."status" is '搜索状态';
comment on column "search_task"."ret_summary" is '搜索结果批次摘要';
comment on column "search_task"."ext_info" is '扩展字段';
comment on column "search_task"."create_time" is '创建时间';
comment on column "search_task"."modify_time" is '修改时间';


-- 搜索结果图片
DROP TABLE IF EXISTS search_result_img;
CREATE TABLE "search_result_img" (
  "id" SERIAL PRIMARY KEY,
  "search_id" INTEGER,
  "image_id" INTEGER,
  "image_url" VARCHAR,
  "image_show_url" VARCHAR,
  "image_caption_id" INTEGER,
  "genre" VARCHAR,
  "bg_cluster_key" VARCHAR,
  "idx_in_cluster" INTEGER,
  "style_similarity" FLOAT,
  "match_score" FLOAT,
  "ext_info" VARCHAR,
  "create_time" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "modify_time" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

comment on table "search_result_img" is '搜索结果图片';
comment on column "search_result_img"."id" is '主键';
comment on column "search_result_img"."search_id" is '搜索记录id';
comment on column "search_result_img"."image_id" is '图片id';
comment on column "search_result_img"."image_url" is '图片url';
comment on column "search_result_img"."image_show_url" is '图片展示url';
comment on column "search_result_img"."image_caption_id" is '图片打标id';
comment on column "search_result_img"."genre" is '图片类别';
comment on column "search_result_img"."bg_cluster_key" is '背景聚类簇唯一标识';
comment on column "search_result_img"."idx_in_cluster" is '在背景聚类簇中的图片索引序号，从0开始';
comment on column "search_result_img"."style_similarity" is '风格相似度';
comment on column "search_result_img"."match_score" is '匹配得分';
comment on column "search_result_img"."ext_info" is '扩展字段';
comment on column "search_result_img"."create_time" is '创建时间';
comment on column "search_result_img"."modify_time" is '修改时间';

CREATE INDEX idx_search_result_img_search_id ON search_result_img(search_id);
CREATE INDEX idx_search_result_img_image_id ON search_result_img(image_id);
CREATE INDEX idx_search_result_img_image_caption_id ON search_result_img(image_caption_id);

-- 新增 ext_info 字段
ALTER TABLE "image" ADD COLUMN "ext_info" jsonb NOT NULL DEFAULT '{}'::jsonb;
COMMENT ON COLUMN "image"."ext_info" IS '扩展信息字段，用于存储额外的非结构化数据，如处理人等，格式为 JSONB';

ALTER TABLE image_caption_user ADD COLUMN type VARCHAR DEFAULT '';