spring:
  datasource:
    mysql:
      url: *******************************************************************************************************************************************
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: root
      password: rds_dev@conrain2023
    pgsql:
      url: ********************************************************************************************************************************
      driver-class-name: org.postgresql.Driver
      username: dev_pg_root
      password: pg_rds@conrain2025
  redis:
    host: r-8vbgj3jeslg143yol4.redis.zhangbei.rds.aliyuncs.com
    password: r-8vbgj3jeslg143yol4:Tair-dev@conrain
  schedulerx2:
    appKey: cU4391BmpicGW0Vll28duKg
    endpoint: addr-cn-zhangjiakou-internal.edas.aliyun.com
    groupId: aigc-platform
    namespace: 0e68ed47-7bbf-47ae-a421-07c16656846c
  jackson:
    default-property-inclusion: NON_NULL
  servlet:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB
app:
  custom:
    tenant: MuseGate
    env: DEV
    domain: https://aigc-dev.conrain.cn
aliyun:
  sts:
    endpoint: sts-vpc.cn-zhangjiakou.aliyuncs.com
  oss:
    bucket: aigc-platform-dev
  sms:
    accessKeyId: LTAI5tDoz5LzRafiN33aKkwj
    accessKeySecret: ******************************
    regionId: cn-zhangjiakou
    endpoint: dysmsapi.aliyuncs.com
    #    短信模板绑定的签名要和这里一致
    signName: 霖润智能
    mock: false
  security:
    accessKeyId: LTAI5tKiqrJPS3ih4ynvheHq
    accessKeySecret: ******************************
    endpoint: green-cip.cn-hangzhou.aliyuncs.com
  ice:
    callback: https://aigc-dev.conrain.cn/media/notify

zlpay:
  url: https://ysczlpay.zlinepay.com/payGateway/api
  merchantNo: M230926211
  appId: 47a9912dd189440ea5c2c26f13bdd6ca
  merchantPriKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBALZ0KihHpvvcPHJJX3NTc/sXCe113rHkNez5DP8BkBEo93OcclafcDsVj7A3N7WK/kQBFuGu5gsM9/UK/CQVs+0InKH5x1INb+eN4loyxLJjRavUSB+pXV6bzQX0oZSY/53L7Dwvi+wwl1+WUQXB5bYljaKFnEm2hfIkVV5VDUD3AgMBAAECgYBAPNIC8IdIMZhOnKqwjfdNtiTWqCNJ+pFJ5729oq04fXXyDGjtOqFnDAZVAnvovREcnE2UE+IGjgBXFBEGG2YHPDKb7DFopDSBqKeWiOqoD6ZGV0a0awEMg9hvIij6L1fyB0wtsMREnoSlHX3FQD7k5nNUWd1RYThq1d/AxUNHQQJBAOAKMJbLPDqX4eVFRvJK6jnz5cefTE2unTelXAk+sQqApgI1y5rF7iHL/lC+ADiRvsJGyzjVuBa0SMYOmV18z/ECQQDQe0aYsM8tNXaoIj5FizvxVOcHKsVFY1xloY/XGpd8zzv2YLX6tlsLkB2uXYloh8QdfP8jks8mVXdqoSPWhgdnAkBD+HNzXGeCc0/yigkTO01Clc9xt8+jhcVm+4EnwOBfilZTN1T1OPWdRVF715kjkHwqXuYK8TQvlzS4+fbnmRVRAkEAw9fKDY2qURG0EWV5keKlwKK9E+J9xhz8owwOD0cPn1MdROi8inPbOxV6jF2ZwvQmqnzNQCGdLqmCsEwHWnI1aQJAVyrvJTx2rtoLGQb/qB0bdLq8/TcmxUglgXmW4l3rQH+e2oMQMA42E9V4TixIYTFTDGWkc4NcJkLYegEjin77Aw==
  platPubKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAudc1lfbldbQTSpy1slQFL/Di5+wADyBmKsMxVhNemmg4M/xNEbrJXdaNI1WC4oy/OFYSGlg0MOOjwgFii++uKQ8wkjMDIAH4TAy/REazemq9zxt8JzZoJg2Bh0edUkN9bHF1DkJyTvLKLWQwaimW2GvakWcrsZrJuMmv2zxJW1C6eMNyXmbF+KKwmB0eWB/itZR6skQ2svip3hySHpS/RTXrqBWwc8Fvh9Vunm9DnAXV9dYEOrvCKMX8p4OoYk9e4wbrEKvjMFFHfwgZdOL07tuQK8YMP0zBfyFLWV6t9Ii3OpXJlLGQxErNIEWEXR1EhaLP/akCnGnDnGwoureFZQIDAQAB
  notifyUrl: https://kdd-dev.conrain.cn/notify/zlpay/result

nacos:
  config:
    access-key: LTAI5tCGkdjPzZsQfC2Yj3bX
    endpoint: addr-cn-zhangjiakou-internal.edas.aliyun.com
    namespace: bf572797-bc3c-4225-a649-a23a5ed108fa
    secret-key: ******************************

ai:
  qwen:
    service: http://lq.lianqiai.cn:20310/
    sec: none
    timeout: 10
    model: QWen/QWen-14B-Chat
  gpt:
    service: https://api.openai.com/
    sec: ***************************************************
    timeout: 20
    model: gpt-4-1106-preview

llm:
  service.url: http://*************:8089


comfyui:
  service:
    url: http://lq.lianqiai.cn:20318
  ws:
    url: ws://lq.lianqiai.cn:20318
  lora:
    url: http://lq.lianqiai.cn:20319
  output:
    path: /home/<USER>/aigc/ComfyUI/output/
  input:
    path: /home/<USER>/aigc/ComfyUI/input/
  file:
    4090url: http://lq.lianqiai.cn:20310
    a800url: http://lq.lianqiai.cn:20310
    embedWorkflow: true
    fileType: png
    needSyncLoraFile: false


# 微信支付相关参数
wx:
  pay:
    # 接收结果通知地址
    notifyUrl: https://aigc-dev.conrain.cn/wx/pay/notify

rocketmq:
  enabled: true
  name-server: ************:9876
  producer:
    group: aigc-platform-dev-producer-group
  consumer:
    group: aigc-platform-dev-consumer-group

alipay:
  notifyUrl: https://aigc-dev.conrain.cn/alipay/notify



# 美图AI服务配置
meitu:
  api:
    url: https://openapi.mtlab.meitu.com
    endpoints:
      removeWrinkle: /v1/mtrmwrink
  auth:
    appId: 358768
    appKey: ef5269070f0b4e01b833ab0698a53506
    secretId: 29c3894c83e8457e86d0ac1ef48120d0