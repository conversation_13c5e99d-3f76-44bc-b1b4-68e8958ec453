package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.ImageRecordService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.ImageRecordQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageNavigationVO;
import ai.conrain.aigc.platform.service.model.vo.ImageRecordVO;
import jakarta.validation.Valid;
import java.util.Calendar;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Image控制器
 *
 * <AUTHOR>
 * @version ImageService.java v 0.1 2025-07-30 02:33:24
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/imageGallery/imageRecord")
public class ImageRecordController {

	/** imageService */
	@Autowired
	private ImageRecordService imageService;

	@PostMapping("/queryById")
	public Result<ImageRecordVO> queryById(@Valid @RequestBody ImageRecordQuery query) {
        query.setPageNum(1);
        query.setPageSize(1);
        PageInfo<ImageRecordVO> page = imageService.queryImageByPage(query);
        return Result.success(CollectionUtils.isEmpty(page.getList()) ? null : page.getList().get(0));
	}

    @PostMapping("/queryByPage")
    public Result<PageInfo<ImageRecordVO>> getImageByPage(@Valid @RequestBody ImageRecordQuery query) {
        if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
            return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
        }
        // 将 query.createTimeEnd 设置为当天最后一秒
        if (query.getCreateTimeEnd() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(query.getCreateTimeEnd());
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            query.setCreateTimeEnd(calendar.getTime());
        }
        return Result.success(imageService.queryImageByPage(query));
    }

    @PostMapping("/queryTags")
    public Result<List<String>> queryImageTags(@Valid @RequestBody ImageRecordQuery query){
        return Result.success(imageService.queryImageTags(query));
    }

    @PostMapping("/navigation")
	public Result<ImageNavigationVO> queryNavigation(@Valid @RequestBody ImageRecordQuery query) {

		if (query == null) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(imageService.queryNavigation(query));
	}
}
